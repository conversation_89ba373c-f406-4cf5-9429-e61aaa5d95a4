"""
AI处理线程 - 负责后台AI图像解析处理
"""

import os
from PyQt5.QtCore import QThread, pyqtSignal

from modules.ai_processor import AIProcessor


class AIProcessingThread(QThread):
    """AI处理线程"""
    progress_updated = pyqtSignal(int, str)  # 进度, 消息
    result_ready = pyqtSignal(str, dict)     # 文件路径, 结果

    def __init__(self, files, ai_config, prompt):
        super().__init__()
        self.files = files
        self.ai_config = ai_config
        self.prompt = prompt
        self.ai_processor = AIProcessor()

    def run(self):
        """运行AI处理"""
        for i, file_path in enumerate(self.files):
            try:
                # 降级为DEBUG级别，这是处理进度信息
                self.progress_updated.emit(i, f"[DEBUG] 正在处理: {os.path.basename(file_path)}")

                # 配置AI处理器
                self.ai_processor.configure(self.ai_config)

                # 处理文件
                result = self.ai_processor.process_image(file_path, self.prompt)
                self.result_ready.emit(file_path, result)

            except Exception as e:
                error_result = {
                    "success": False,
                    "error": str(e),
                    "file_path": file_path
                }
                self.result_ready.emit(file_path, error_result)
                self.progress_updated.emit(i, f"处理失败 {os.path.basename(file_path)}: {str(e)}")

        self.progress_updated.emit(len(self.files), "处理完成") 