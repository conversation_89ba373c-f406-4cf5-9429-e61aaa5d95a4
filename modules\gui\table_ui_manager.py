"""
表格UI管理器 - 负责结果表格、数据显示管理
重构自原始pyqt5_main_gui.py的表格处理部分
"""

import os
import time
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from modules.ui_constants import UIConstants
from modules.table_manager import TableManager


class TableUIManager:
    """表格UI管理器"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        self.current_table_file_path = None  # 🔥 新增：追踪当前表格对应的文件路径
        
    def create_center_table_panel(self, parent):
        """创建中间商品明细表格区域"""
        center_widget = QWidget()
        parent.addWidget(center_widget)

        center_layout = QVBoxLayout(center_widget)
        center_layout.setContentsMargins(5, 5, 5, 5)
        center_layout.setSpacing(10)

        # 商品明细表格区域
        self.create_result_table_section(center_layout)

    def create_result_table_section(self, parent):
        """创建结果表格区域"""
        table_container = QWidget()
        table_container.setStyleSheet("""
            QWidget {
                background-color: transparent;
                font-family: 'Microsoft YaHei';
            }
        """)
        parent.addWidget(table_container)

        table_layout = QVBoxLayout(table_container)
        table_layout.setContentsMargins(0, 0, 0, 0)
        table_layout.setSpacing(8)

        # 创建表格标签页
        self.table_tab_widget = QTabWidget()

        # 🔥 修复：优化表格标签页样式，解决字体模糊和显示不全问题
        self.table_tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #3a3a3a;
                background-color: #161617;
                border-radius: 6px;
                top: -1px;
            }
            QTabBar::tab {
                background-color: #161617;
                color: #ffffff;
                padding: 6px 16px;
                margin-right: 1px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
                font-weight: 500;
                font-size: 11px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                min-height: 22px;
                max-height: 22px;
                min-width: 85px;
                text-align: center;
            }
            QTabBar::tab:selected {
                background-color: #0071e3;
                color: white;
                font-weight: 600;
            }
            QTabBar::tab:hover:!selected {
                background-color: #3a3a3a;
            }
        """)

        table_layout.addWidget(self.table_tab_widget)

        # 创建取货表格
        self.create_pickup_table()

        # 创建退货表格
        self.create_return_table()

        # 注意：解析结果表格现在在右侧面板中，不再在这里创建

    def create_pickup_table(self):
        """创建取货表格"""
        pickup_tab = QWidget()
        self.table_tab_widget.addTab(pickup_tab, "拿货商品")

        pickup_layout = QVBoxLayout(pickup_tab)
        pickup_layout.setContentsMargins(5, 5, 5, 5)

        # 创建取货表格
        self.pickup_table = QTableWidget()
        self.setup_table_properties(self.pickup_table, "pickup")
        pickup_layout.addWidget(self.pickup_table)

        # 拿货商品操作按钮
        pickup_erp_layout = QHBoxLayout()

        # 🔥 按钮顺序1：查询ERP - 保持绿色
        self.pickup_query_erp_btn = QPushButton("查询ERP")
        self.pickup_query_erp_btn.clicked.connect(lambda: self.query_erp_for_table("pickup"))
        self.pickup_query_erp_btn.setMinimumHeight(30)
        self.pickup_query_erp_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #3a3a3a;
                color: #888888;
            }
        """)
        pickup_erp_layout.addWidget(self.pickup_query_erp_btn)

        # 🔥 按钮顺序2：查询选中 - 改为蓝色
        self.pickup_query_selected_btn = QPushButton("查询选中")
        self.pickup_query_selected_btn.clicked.connect(lambda: self.query_erp_for_selected_rows("pickup"))
        self.pickup_query_selected_btn.setMinimumHeight(30)
        self.pickup_query_selected_btn.setToolTip("查询选中行的ERP信息，方便修改后重新查询")
        self.pickup_query_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QPushButton:disabled {
                background-color: #3a3a3a;
                color: #888888;
            }
        """)
        pickup_erp_layout.addWidget(self.pickup_query_selected_btn)

        # 🔥 按钮顺序3：上传成本（原更新成本）- 改为蓝色
        self.pickup_update_erp_btn = QPushButton("上传成本")
        self.pickup_update_erp_btn.clicked.connect(lambda: self.update_erp_costs_for_table("pickup"))
        self.pickup_update_erp_btn.setMinimumHeight(30)
        self.pickup_update_erp_btn.setEnabled(False)  # 默认禁用，查询后启用
        # 🔥 新增：添加工具提示
        self.pickup_update_erp_btn.setToolTip("上传成本价到ERP系统\n提示：按住Ctrl键点击可强制更新无变化的商品")
        self.pickup_update_erp_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QPushButton:disabled {
                background-color: #3a3a3a;
                color: #888888;
            }
        """)
        pickup_erp_layout.addWidget(self.pickup_update_erp_btn)

        # 🔥 按钮顺序4：中止操作 - 保持红色样式
        self.pickup_abort_btn = QPushButton("中止操作")
        self.pickup_abort_btn.clicked.connect(lambda: self.abort_erp_operations("pickup"))
        self.pickup_abort_btn.setMinimumHeight(30)
        self.pickup_abort_btn.setEnabled(False)  # 默认禁用，操作进行时启用
        self.pickup_abort_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
            QPushButton:disabled {
                background-color: #3a3a3a;
                color: #888888;
            }
        """)
        pickup_erp_layout.addWidget(self.pickup_abort_btn)

        # 🔥 按钮顺序5：添加新行 - 去掉绿色，改为蓝色
        self.pickup_add_row_btn = QPushButton("添加新行")
        self.pickup_add_row_btn.clicked.connect(lambda: self.add_new_row_to_table(self.pickup_table, "pickup"))
        self.pickup_add_row_btn.setMinimumHeight(30)
        self.pickup_add_row_btn.setToolTip("手动添加新的商品行，用于补充AI解析遗漏的商品")
        self.pickup_add_row_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        pickup_erp_layout.addWidget(self.pickup_add_row_btn)

        pickup_erp_layout.addStretch()
        pickup_layout.addLayout(pickup_erp_layout)

        # 表格管理器
        self.main_window.pickup_table_manager = TableManager(self.pickup_table)

    def create_return_table(self):
        """创建退货表格"""
        return_tab = QWidget()
        self.table_tab_widget.addTab(return_tab, "退货商品")

        return_layout = QVBoxLayout(return_tab)
        return_layout.setContentsMargins(5, 5, 5, 5)

        # 创建退货表格
        self.return_table = QTableWidget()
        self.setup_table_properties(self.return_table, "return")
        return_layout.addWidget(self.return_table)

        # 退货商品操作按钮
        return_erp_layout = QHBoxLayout()

        # 🔥 按钮顺序1：查询ERP - 保持绿色
        self.return_query_erp_btn = QPushButton("查询ERP")
        self.return_query_erp_btn.clicked.connect(lambda: self.query_erp_for_table("return"))
        self.return_query_erp_btn.setMinimumHeight(30)
        self.return_query_erp_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #3a3a3a;
                color: #888888;
            }
        """)
        return_erp_layout.addWidget(self.return_query_erp_btn)

        # 🔥 按钮顺序2：查询选中 - 改为蓝色
        self.return_query_selected_btn = QPushButton("查询选中")
        self.return_query_selected_btn.clicked.connect(lambda: self.query_erp_for_selected_rows("return"))
        self.return_query_selected_btn.setMinimumHeight(30)
        self.return_query_selected_btn.setToolTip("查询选中行的ERP信息，方便修改后重新查询")
        self.return_query_selected_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QPushButton:disabled {
                background-color: #3a3a3a;
                color: #888888;
            }
        """)
        return_erp_layout.addWidget(self.return_query_selected_btn)

        # 🔥 按钮顺序3：上传成本（原更新成本）- 改为蓝色
        self.return_update_erp_btn = QPushButton("上传成本")
        self.return_update_erp_btn.clicked.connect(lambda: self.update_erp_costs_for_table("return"))
        self.return_update_erp_btn.setMinimumHeight(30)
        self.return_update_erp_btn.setEnabled(False)  # 默认禁用，查询后启用
        # 🔥 新增：添加工具提示
        self.return_update_erp_btn.setToolTip("上传成本价到ERP系统\n提示：按住Ctrl键点击可强制更新无变化的商品")
        self.return_update_erp_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
            QPushButton:disabled {
                background-color: #3a3a3a;
                color: #888888;
            }
        """)
        return_erp_layout.addWidget(self.return_update_erp_btn)

        # 🔥 按钮顺序4：中止操作 - 保持红色样式
        self.return_abort_btn = QPushButton("中止操作")
        self.return_abort_btn.clicked.connect(lambda: self.abort_erp_operations("return"))
        self.return_abort_btn.setMinimumHeight(30)
        self.return_abort_btn.setEnabled(False)  # 默认禁用，操作进行时启用
        self.return_abort_btn.setStyleSheet("""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #c82333;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
            QPushButton:disabled {
                background-color: #3a3a3a;
                color: #888888;
            }
        """)
        return_erp_layout.addWidget(self.return_abort_btn)

        # 🔥 按钮顺序5：添加新行 - 去掉绿色，改为蓝色
        self.return_add_row_btn = QPushButton("添加新行")
        self.return_add_row_btn.clicked.connect(lambda: self.add_new_row_to_table(self.return_table, "return"))
        self.return_add_row_btn.setMinimumHeight(30)
        self.return_add_row_btn.setToolTip("手动添加新的商品行，用于补充AI解析遗漏的商品")
        self.return_add_row_btn.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)
        return_erp_layout.addWidget(self.return_add_row_btn)

        return_erp_layout.addStretch()
        return_layout.addLayout(return_erp_layout)

        # 表格管理器
        self.main_window.return_table_manager = TableManager(self.return_table)

    def create_result_table(self):
        """创建解析结果表格（已移到右侧面板，此方法保留用于兼容性）"""
        # 此方法现在由主界面控制器在右侧面板中调用
        # 保留此方法以维持代码兼容性，但实际创建逻辑已移动
        pass

    def setup_table_properties(self, table, table_type):
        """设置表格属性"""
        # 设置列（两列退货率格式：15退、30退）
        columns = ["行数", "款号", "颜色规格", "数量", "单价", "小计", "状态", "利润", "15退", "30退"]
        table.setColumnCount(len(columns))
        table.setHorizontalHeaderLabels(columns)

        # 设置表格属性
        table.setAlternatingRowColors(True)
        table.setSelectionBehavior(QAbstractItemView.SelectRows)
        # 🔥 重要修复：设置为扩展选择模式，只有按住Ctrl时才能多选
        table.setSelectionMode(QAbstractItemView.ExtendedSelection)
        table.horizontalHeader().setStretchLastSection(False)  # 🔥 修改：禁用自动拉伸，使用固定列宽
        table.verticalHeader().setVisible(False)
        # 允许双击编辑，但只有款号列可编辑（在添加商品时设置）
        table.setEditTriggers(QAbstractItemView.DoubleClicked)

        # 🔥 强制设置网格线显示和样式
        table.setShowGrid(True)
        table.setGridStyle(Qt.SolidLine)

        # 设置表格样式
        if table_type == "pickup":
            border_color = "#4CAF50"
            header_color = "#4CAF50"
        else:
            border_color = "#F44336"
            header_color = "#F44336"

        table.setStyleSheet(f"""
            QTableWidget {{
                background-color: #161617;
                color: white;
                gridline-color: #555555;
                selection-background-color: #0071e3;
                border: 1px solid {border_color};
                border-radius: 6px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 12px;
                outline: none;
            }}
            QTableWidget::item {{
                background-color: #161617;
                /* 🔥 修复：删除强制的color设置，让setForeground()生效 */
                border: none;
                border-right: 1px solid #555555;
                border-bottom: 1px solid #555555;
                padding: 4px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 12px;
                text-align: center;
                outline: none;
            }}
            QTableWidget::item:selected {{
                background-color: #0071e3;
                color: white;
                border-right: 1px solid #555555;
                border-bottom: 1px solid #555555;
            }}
            QTableWidget QLineEdit {{
                background-color: #1a1a1b;
                color: white;
                border: 1px solid #0071e3;
                border-radius: 4px;
                padding: 4px;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }}
            QHeaderView::section {{
                background-color: {header_color};
                color: white;
                padding: 8px;
                border: none;
                border-right: 1px solid #555555;
                border-bottom: 1px solid #555555;
                font-weight: bold;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 12px;
            }}
        """)

        # 连接表格行点击事件
        table.itemClicked.connect(self.on_table_row_clicked)

        # 连接表格项变更事件，用于自动计算小计
        table.itemChanged.connect(lambda item: self.on_table_item_changed(table, item))



    def on_table_selection_changed(self):
        """表格选择变更事件"""
        # 获取当前选中的行
        current_table = self.get_current_table()
        if current_table:
            selected_rows = current_table.selectionModel().selectedRows()
            if selected_rows:
                row = selected_rows[0].row()
                self.main_window.log_message(f"选中表格行: {row + 1}")

    def get_current_table(self):
        """获取当前表格"""
        current_index = self.table_tab_widget.currentIndex()
        if current_index == 0:
            return self.pickup_table
        elif current_index == 1:
            return self.return_table
        return None

    def get_current_table_type(self):
        """获取当前表格类型"""
        current_index = self.table_tab_widget.currentIndex()
        if current_index == 0:
            return "pickup"
        elif current_index == 1:
            return "return"
        return "pickup"

    def add_result_to_table(self, file_path, result):
        """将解析结果添加到商品明细表格"""
        try:
            # 🔥 新增：更新当前表格文件路径
            self.current_table_file_path = file_path
            
            # 提取结果信息
            if isinstance(result, dict) and result.get("success") and "data" in result:
                ticket_data = result["data"]
            elif isinstance(result, dict) and "items" in result:
                ticket_data = result
            else:
                return

            # 确定表格类型
            items = ticket_data.get("items", [])
            if not items:
                return

            # 检查是否有负数量（退货）
            has_negative = any(float(item.get("数量", 0)) < 0 for item in items)
            
            if has_negative:
                # 分离拿货和退货商品
                pickup_items = [item for item in items if float(item.get("数量", 0)) >= 0]
                return_items = [item for item in items if float(item.get("数量", 0)) < 0]
                
                # 添加到对应表格
                if pickup_items:
                    self.add_items_to_table(self.pickup_table, pickup_items, file_path)
                if return_items:
                    self.add_items_to_table(self.return_table, return_items, file_path)
            else:
                # 全部添加到拿货表格
                self.add_items_to_table(self.pickup_table, items, file_path)

            # 设置供应商信息
            supplier = ticket_data.get("supplier", "未知")
            self.main_window.log_message(f"已加载商品明细: {len(items)} 个商品，供应商: {supplier}")

            # 检查并启用合并按钮
            self.check_and_enable_merge_button()

        except Exception as e:
            self.main_window.log_message(f"添加解析结果失败: {str(e)}", "ERROR")

    def add_items_to_table(self, table, items, file_path):
        """添加商品到指定表格"""
        for item in items:
            try:
                row = table.rowCount()
                table.insertRow(row)

                # 行数
                row_item = QTableWidgetItem(str(row + 1))
                row_item.setTextAlignment(Qt.AlignCenter)
                row_item.setForeground(QColor(255, 255, 255))  # 🔥 设置默认白色字体
                table.setItem(row, 0, row_item)

                # 款号 - 允许编辑
                sku_code = str(item.get("款号", ""))
                sku_item = QTableWidgetItem(sku_code)
                sku_item.setTextAlignment(Qt.AlignCenter)
                sku_item.setForeground(QColor(255, 255, 255))  # 🔥 设置默认白色字体
                sku_item.setFlags(sku_item.flags() | Qt.ItemIsEditable)
                table.setItem(row, 1, sku_item)

                # 颜色规格
                spec = str(item.get("颜色规格", ""))
                spec_item = QTableWidgetItem(spec)
                spec_item.setTextAlignment(Qt.AlignCenter)
                spec_item.setForeground(QColor(255, 255, 255))  # 🔥 设置默认白色字体
                table.setItem(row, 2, spec_item)

                # 数量
                quantity = str(item.get("数量", ""))
                quantity_item = QTableWidgetItem(quantity)
                quantity_item.setTextAlignment(Qt.AlignCenter)
                quantity_item.setForeground(QColor(255, 255, 255))  # 🔥 设置默认白色字体
                table.setItem(row, 3, quantity_item)

                # 单价
                unit_price = str(item.get("单价", ""))
                price_item = QTableWidgetItem(unit_price)
                price_item.setTextAlignment(Qt.AlignCenter)
                price_item.setForeground(QColor(255, 255, 255))  # 🔥 设置默认白色字体
                table.setItem(row, 4, price_item)

                # 小计
                total = str(item.get("小计", ""))
                total_item = QTableWidgetItem(total)
                total_item.setTextAlignment(Qt.AlignCenter)
                total_item.setForeground(QColor(255, 255, 255))  # 🔥 设置默认白色字体
                table.setItem(row, 5, total_item)

                # 状态
                status_item = QTableWidgetItem("未查询")
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setForeground(QColor(255, 255, 255))  # 🔥 设置默认白色字体
                table.setItem(row, 6, status_item)

                # 利润
                profit_item = QTableWidgetItem("未计算")
                profit_item.setTextAlignment(Qt.AlignCenter)
                profit_item.setForeground(QColor(255, 255, 255))  # 🔥 设置默认白色字体
                table.setItem(row, 7, profit_item)

                # 退货率
                return_rate_item = QTableWidgetItem("-")
                return_rate_item.setTextAlignment(Qt.AlignCenter)
                return_rate_item.setForeground(QColor(255, 255, 255))  # 🔥 设置默认白色字体
                table.setItem(row, 8, return_rate_item)

            except Exception as e:
                self.main_window.log_message(f"添加商品到表格失败: {str(e)}", "ERROR")

    def update_table_row_status(self, table, row, erp_status=None, match_status=None, profit_status=None):
        """更新表格行状态"""
        try:
            if erp_status is not None:
                status_item = QTableWidgetItem(erp_status)
                status_item.setTextAlignment(Qt.AlignCenter)

                # 🔥 新增：应用状态颜色规则
                if "📈" in erp_status:
                    status_item.setForeground(QColor(244, 67, 54))  # 红色字体（价格上涨）
                    print(f"🎨 设置上涨状态颜色: {erp_status} -> 红色")
                elif "📉" in erp_status:
                    status_item.setForeground(QColor(76, 175, 80))  # 绿色字体（价格下降）
                    print(f"🎨 设置下降状态颜色: {erp_status} -> 绿色")
                elif "✅" in erp_status:
                    status_item.setForeground(QColor(76, 175, 80))  # 绿色字体（成功）
                    print(f"🎨 设置成功状态颜色: {erp_status} -> 绿色")
                elif "🟡" in erp_status or "待确认" in erp_status:
                    status_item.setForeground(QColor(255, 152, 0))  # 橙色字体（待确认）
                    print(f"🎨 设置待确认状态颜色: {erp_status} -> 橙色")
                elif "❌" in erp_status or "⚠️" in erp_status:
                    status_item.setForeground(QColor(244, 67, 54))  # 红色字体（错误）
                    print(f"🎨 设置错误状态颜色: {erp_status} -> 红色")
                else:
                    status_item.setForeground(QColor(255, 255, 255))  # 白色字体（默认）
                    print(f"🎨 设置默认状态颜色: {erp_status} -> 白色")

                table.setItem(row, 6, status_item)

            if match_status is not None:
                match_item = QTableWidgetItem(match_status)
                match_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 7, match_item)

            if profit_status is not None:
                profit_item = QTableWidgetItem(profit_status)
                profit_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 8, profit_item)

        except Exception as e:
            self.main_window.log_message(f"更新表格行状态失败: {str(e)}", "ERROR")

    def clear_table(self, table):
        """清空表格"""
        table.setRowCount(0)

    def clear_all_tables(self):
        """清空所有表格"""
        self.clear_table(self.pickup_table)
        self.clear_table(self.return_table)

    def on_table_row_clicked(self, item):
        """表格行点击事件"""
        try:
            if not item:
                print("❌ 点击事件：item为空")
                return

            row = item.row()
            table = item.tableWidget()
            print(f"🖱️ 表格行点击事件：第{row + 1}行")
            self.main_window.log_message(f"选中表格行: {row + 1}")

            # 检查是否有颜色确认面板
            if hasattr(self.main_window, 'color_confirm_panel'):
                print(f"✅ 找到颜色确认面板")

                # 获取行的基本信息
                sku_item = table.item(row, 1)  # 款号列
                status_item = table.item(row, 6)  # 状态列
                price_item = table.item(row, 4)  # 单价列

                print(f"📋 行数据检查：")
                print(f"   - 款号: {sku_item.text() if sku_item else 'None'}")
                print(f"   - 状态: {status_item.text() if status_item else 'None'}")
                print(f"   - 单价: {price_item.text() if price_item else 'None'}")

                if sku_item and sku_item.text().strip():
                    sku_code = sku_item.text().strip()
                    status_text = status_item.text() if status_item else ""

                    try:
                        ticket_price = float(price_item.text()) if price_item else 0.0
                    except (ValueError, TypeError):
                        ticket_price = 0.0

                    print(f"🔍 状态检查：'{status_text}'")
                    print(f"   - 包含🤔: {'🤔' in status_text}")
                    print(f"   - 包含待确认: {'待确认' in status_text}")
                    print(f"   - 包含📈: {'📈' in status_text}")
                    print(f"   - 包含📉: {'📉' in status_text}")
                    print(f"   - 包含✅: {'✅' in status_text}")

                    # 🔥 重要修复：检查是否为待确认状态或已确认状态
                    is_pending_confirm = "待确认" in status_text
                    is_confirmed = any(indicator in status_text for indicator in ["📈", "📉", "✅"])

                    if is_pending_confirm or is_confirmed:
                        if is_pending_confirm:
                            print(f"✅ 识别为待确认状态")
                        else:
                            print(f"✅ 识别为已确认状态，允许查看确认详情")

                        # 检查ERP匹配结果
                        print(f"🔍 检查ERP匹配结果...")
                        print(f"   - 有erp_match_results属性: {hasattr(self.main_window, 'erp_match_results')}")
                        if hasattr(self.main_window, 'erp_match_results'):
                            print(f"   - erp_match_results内容: {list(self.main_window.erp_match_results.keys())}")
                            print(f"   - 第{row}行在结果中: {row in self.main_window.erp_match_results}")

                        # 尝试从ERP匹配结果中获取颜色分组信息
                        if hasattr(self.main_window, 'erp_match_results') and row in self.main_window.erp_match_results:
                            match_result = self.main_window.erp_match_results[row]
                            color_groups = match_result.get("color_groups", {})
                            table_color_spec = match_result.get("table_color_spec", "")
                            strategy = match_result.get("strategy", "")

                            print(f"📊 ERP匹配结果：")
                            print(f"   - 颜色分组数量: {len(color_groups)}")
                            print(f"   - 颜色分组: {list(color_groups.keys())}")
                            print(f"   - 表格颜色规格: {table_color_spec}")
                            print(f"   - 策略: {strategy}")

                            if color_groups:
                                print(f"🎨 开始加载确认数据到面板...")
                                # 加载确认数据到固定面板，传递表格中的颜色规格和策略信息
                                self.main_window.color_confirm_panel.load_confirmation_data(
                                    row, sku_code, color_groups, ticket_price, table_color_spec, strategy
                                )
                                print(f"✅ 确认数据加载完成")

                                # 🔥 重要修复：尝试恢复之前保存的确认状态
                                self.restore_color_confirm_state_for_row(row)

                                # 🎨 立即尝试恢复确认状态（在卡片创建/保持后）
                                if hasattr(self.main_window, 'current_selected_file') and self.main_window.current_selected_file:
                                    print(f"🔄 立即恢复第{row+1}行的确认状态...")
                                    # 使用QTimer确保在界面更新后恢复状态
                                    from PyQt5.QtCore import QTimer
                                    QTimer.singleShot(50, lambda: self.restore_color_confirm_state(self.main_window.current_selected_file, row))
                            else:
                                print(f"❌ 颜色分组信息为空")
                                self.main_window.log_message(f"第{row+1}行缺少颜色分组信息", "WARNING")
                                self.main_window.color_confirm_panel.show_empty_state()
                        else:
                            print(f"❌ 没有找到ERP匹配结果")
                            # 检查是否正在进行ERP查询
                            if hasattr(self.main_window, 'erp_ui_manager') and hasattr(self.main_window.erp_ui_manager, 'erp_query_thread'):
                                if self.main_window.erp_ui_manager.erp_query_thread and self.main_window.erp_ui_manager.erp_query_thread.isRunning():
                                    print(f"⏳ ERP查询正在进行中，显示等待状态")
                                    self.show_waiting_state(row, sku_code)
                                    self.main_window.log_message(f"第{row+1}行ERP查询进行中，请稍候", "INFO")
                                else:
                                    print(f"❌ ERP查询已完成但没有结果")
                                    # 检查是否需要重新查询
                                    if len(self.main_window.erp_match_results) == 0:
                                        print(f"💡 提示：可能需要重新进行ERP查询")
                                        self.main_window.color_confirm_panel.show_empty_state()
                                    else:
                                        self.main_window.color_confirm_panel.show_empty_state()
                                        self.main_window.log_message(f"第{row+1}行没有ERP匹配结果", "INFO")
                            else:
                                print(f"❌ 没有ERP查询线程")
                                self.main_window.color_confirm_panel.show_empty_state()
                                self.main_window.log_message(f"第{row+1}行没有ERP匹配结果，建议重新进行ERP查询", "WARNING")
                    else:
                        print(f"ℹ️ 不是待确认或已确认状态，显示空状态")
                        # 不是待确认或已确认状态，显示空状态
                        self.main_window.color_confirm_panel.show_empty_state()
                else:
                    print(f"❌ 款号为空")
                    # 显示空状态
                    self.main_window.color_confirm_panel.show_empty_state()
            else:
                print(f"❌ 没有找到颜色确认面板")
                # 显示空状态
                if hasattr(self.main_window, 'color_confirm_panel'):
                    self.main_window.color_confirm_panel.show_empty_state()

        except Exception as e:
            print(f"❌ 表格行点击事件处理异常: {str(e)}")
            import traceback
            print(f"❌ 异常详情: {traceback.format_exc()}")
            self.main_window.log_message(f"处理表格行点击事件时出错: {str(e)}", "ERROR")

    def restore_color_confirm_state(self, file_path, row):
        """恢复颜色确认状态（兼容性方法）"""
        try:
            if hasattr(self.main_window, 'restore_color_confirm_state'):
                self.main_window.restore_color_confirm_state(file_path, row)
        except Exception as e:
            print(f"恢复确认状态失败: {str(e)}")

    def restore_color_confirm_state_for_row(self, row):
        """为指定行恢复颜色确认状态"""
        try:
            # 获取当前选中的文件路径
            current_file = getattr(self.main_window, 'current_selected_file', None)
            if not current_file:
                # 尝试从图像管理器获取当前图像
                if hasattr(self.main_window, 'image_manager'):
                    current_file = self.main_window.image_manager.current_image_path

            if current_file:
                print(f"🔄 尝试恢复第{row+1}行的确认状态，文件: {os.path.basename(current_file)}")

                # 检查是否有保存的确认状态
                if (hasattr(self.main_window, 'color_confirm_states') and
                    current_file in self.main_window.color_confirm_states and
                    row in self.main_window.color_confirm_states[current_file]):

                    confirm_data = self.main_window.color_confirm_states[current_file][row]
                    print(f"✅ 找到确认状态数据: {confirm_data.get('selected_colors', [])}")

                    # 延迟恢复状态，确保面板已完全加载
                    from PyQt5.QtCore import QTimer
                    QTimer.singleShot(50, lambda: self.main_window.restore_color_confirm_state(current_file, row))
                else:
                    print(f"🔍 第{row+1}行没有保存的确认状态")
            else:
                print(f"⚠️ 无法获取当前文件路径，跳过状态恢复")

        except Exception as e:
            print(f"❌ 恢复确认状态失败: {str(e)}")
            import traceback
            print(f"❌ 错误详情: {traceback.format_exc()}")

    def show_waiting_state(self, row, sku_code):
        """显示等待状态"""
        try:
            if hasattr(self.main_window, 'color_confirm_panel'):
                # 创建等待状态的临时数据
                waiting_color_groups = {
                    "正在查询中...": [
                        {
                            "sku_code": f"{sku_code}-LOADING",
                            "sale_price": 0,
                            "cost_price": 0,
                            "pic_big": ""
                        }
                    ]
                }

                print(f"⏳ 显示等待状态：第{row+1}行，款号{sku_code}")
                self.main_window.color_confirm_panel.load_confirmation_data(
                    row, sku_code, waiting_color_groups, 0.0, "查询中...", "waiting"
                )

                # 设置定时器，定期检查ERP查询是否完成
                from PyQt5.QtCore import QTimer
                if not hasattr(self, '_check_timer'):
                    self._check_timer = QTimer()
                    self._check_timer.timeout.connect(lambda: self.check_erp_query_completion(row, sku_code))

                self._check_timer.start(1000)  # 每秒检查一次

        except Exception as e:
            print(f"显示等待状态失败: {str(e)}")

    def check_erp_query_completion(self, row, sku_code):
        """检查ERP查询是否完成"""
        try:
            # 检查ERP查询是否还在运行
            if hasattr(self.main_window, 'erp_ui_manager') and hasattr(self.main_window.erp_ui_manager, 'erp_query_thread'):
                if self.main_window.erp_ui_manager.erp_query_thread and self.main_window.erp_ui_manager.erp_query_thread.isRunning():
                    # 还在运行，继续等待
                    return

            # 查询完成，停止定时器
            if hasattr(self, '_check_timer'):
                self._check_timer.stop()

            # 检查是否有结果
            if hasattr(self.main_window, 'erp_match_results') and row in self.main_window.erp_match_results:
                match_result = self.main_window.erp_match_results[row]
                color_groups = match_result.get("color_groups", {})

                if color_groups:
                    print(f"✅ ERP查询完成，更新确认面板：第{row+1}行")
                    table_color_spec = match_result.get("table_color_spec", "")
                    strategy = match_result.get("strategy", "")

                    # 获取票据价格
                    table = self.get_current_table()
                    price_item = table.item(row, 4) if table else None
                    try:
                        ticket_price = float(price_item.text()) if price_item else 0.0
                    except (ValueError, TypeError):
                        ticket_price = 0.0

                    # 重新加载正确的数据
                    self.main_window.color_confirm_panel.load_confirmation_data(
                        row, sku_code, color_groups, ticket_price, table_color_spec, strategy
                    )
                else:
                    print(f"❌ ERP查询完成但没有颜色分组数据：第{row+1}行")
                    self.main_window.color_confirm_panel.show_empty_state()
            else:
                print(f"❌ ERP查询完成但没有匹配结果：第{row+1}行")
                self.main_window.color_confirm_panel.show_empty_state()

        except Exception as e:
            print(f"检查ERP查询完成状态失败: {str(e)}")
            if hasattr(self, '_check_timer'):
                self._check_timer.stop()

    def query_erp_for_table(self, table_type):
        """查询ERP信息"""
        # 委托给ERP UI管理器处理
        if hasattr(self.main_window, 'erp_ui_manager'):
            self.main_window.erp_ui_manager.query_erp_for_table(table_type)
        else:
            self.main_window.log_message("ERP管理器未初始化", "ERROR")

    def query_erp_for_selected_rows(self, table_type):
        """查询选中行的ERP信息"""
        # 委托给ERP UI管理器处理
        if hasattr(self.main_window, 'erp_ui_manager'):
            self.main_window.erp_ui_manager.query_erp_for_selected_rows(table_type)
        else:
            self.main_window.log_message("ERP管理器未初始化", "ERROR")

    def update_erp_costs_for_table(self, table_type):
        """更新ERP成本价"""
        # 委托给ERP UI管理器处理
        if hasattr(self.main_window, 'erp_ui_manager'):
            self.main_window.erp_ui_manager.update_erp_costs_for_table(table_type)
        else:
            self.main_window.log_message("ERP管理器未初始化", "ERROR")

    def abort_erp_operations(self, table_type):
        """中止ERP操作"""
        # 委托给ERP UI管理器处理
        if hasattr(self.main_window, 'erp_ui_manager'):
            self.main_window.erp_ui_manager.abort_erp_operations(table_type)
        else:
            self.main_window.log_message("ERP管理器未初始化", "ERROR")

    def get_table_by_type(self, table_type):
        """根据类型获取表格"""
        if table_type == "pickup":
            return self.pickup_table
        elif table_type == "return":
            return self.return_table
        return None

    def get_current_table(self):
        """获取当前活动的表格"""
        try:
            if hasattr(self, 'table_tab_widget'):
                current_tab_index = self.table_tab_widget.currentIndex()
                if current_tab_index == 0:
                    return self.pickup_table
                elif current_tab_index == 1:
                    return self.return_table
                else:
                    return self.pickup_table  # 默认返回pickup表格
            else:
                return self.pickup_table
        except Exception as e:
            print(f"获取当前表格失败: {str(e)}")
            return self.pickup_table

    def get_erp_buttons_by_type(self, table_type):
        """根据类型获取ERP操作按钮"""
        if table_type == "pickup":
            return {
                'query_erp': self.pickup_query_erp_btn,
                'query_selected': self.pickup_query_selected_btn,
                'update_erp': self.pickup_update_erp_btn,
                'abort': self.pickup_abort_btn
            }
        elif table_type == "return":
            return {
                'query_erp': self.return_query_erp_btn,
                'query_selected': self.return_query_selected_btn,
                'update_erp': self.return_update_erp_btn,
                'abort': self.return_abort_btn
            }
        return {}

    def enable_erp_buttons(self, table_type, query=None, query_selected=None, update=None, abort=None):
        """启用/禁用ERP操作按钮"""
        buttons = self.get_erp_buttons_by_type(table_type)
        
        if query is not None and 'query_erp' in buttons:
            buttons['query_erp'].setEnabled(query)
        if query_selected is not None and 'query_selected' in buttons:
            buttons['query_selected'].setEnabled(query_selected)
        if update is not None and 'update_erp' in buttons:
            buttons['update_erp'].setEnabled(update)
        if abort is not None and 'abort' in buttons:
            buttons['abort'].setEnabled(abort)

    def merge_split_results(self):
        """合并分割后的解析结果"""
        try:
            result_table = getattr(self.main_window, 'result_table', None)
            stored_results = getattr(self.main_window, 'stored_results', {})
            
            if not result_table or result_table.rowCount() == 0:
                self.main_window.log_message("没有可合并的数据", "WARNING")
                return

            self.main_window.log_message("🔄 开始合并分割结果...")

            # 收集所有分割结果
            split_groups = {}  # 按原始文件名分组

            for row in range(result_table.rowCount()):
                filename_item = result_table.item(row, 1)  # 文件名列
                if not filename_item:
                    continue

                filename = filename_item.text()
                self.main_window.log_message(f"📝 检查文件: {filename}")
                
                # 查找对应的存储结果
                file_path = None
                result = None
                for stored_path, stored_result in stored_results.items():
                    display_name = self.main_window.get_display_name_for_file(stored_path)
                    if display_name == filename:
                        file_path = stored_path
                        result = stored_result
                        break

                if not result:
                    self.main_window.log_message(f"⚠️ 未找到 {filename} 的存储结果", "WARNING")
                    continue

                # 提取原始文件名（去除分割后缀）
                original_name = self.extract_original_filename(filename)
                self.main_window.log_message(f"🔍 {filename} → 原始文件名: {original_name}")

                if original_name not in split_groups:
                    split_groups[original_name] = []

                split_groups[original_name].append({
                    'filename': filename,
                    'result': result,
                    'row': row,
                    'file_path': file_path
                })

            self.main_window.log_message(f"📊 分组结果: {list(split_groups.keys())}")

            # 合并每组的结果
            merged_count = 0
            last_merged_row = None
            
            for original_name, group in split_groups.items():
                if len(group) > 1:  # 只合并有多个分割的组
                    self.main_window.log_message(f"🔗 合并组 '{original_name}': {len(group)} 个文件")
                    merged_result = self.merge_group_results(original_name, group)
                    if merged_result:
                        # 添加合并结果到解析结果表格
                        merged_row = self.add_merged_result_to_result_table(original_name, merged_result)
                        if merged_row is not None:
                            last_merged_row = merged_row
                            merged_count += 1
                            self.main_window.log_message(f"✅ 成功添加合并结果: {original_name}")

            if merged_count > 0:
                self.main_window.log_message(f"🎉 成功合并 {merged_count} 组分割结果")
                
                # 自动选择并加载最后一个合并结果
                if last_merged_row is not None:
                    self.main_window.log_message(f"🎯 自动选择合并结果第 {last_merged_row + 1} 行")
                    result_table.selectRow(last_merged_row)
                    self.load_selected_result_to_table(last_merged_row)
                
                # 禁用合并按钮
                if hasattr(self.main_window, 'merge_tables_btn'):
                    self.main_window.merge_tables_btn.setEnabled(False)
            else:
                self.main_window.log_message("没有找到可合并的分割结果", "WARNING")

        except Exception as e:
            import traceback
            self.main_window.log_message(f"合并表格失败: {str(e)}", "ERROR")
            self.main_window.log_message(f"错误详情: {traceback.format_exc()}", "ERROR")

    def undo_merge_results(self):
        """撤销合并结果"""
        self.main_window.log_message("撤销合并结果功能待实现")

    def extract_original_filename(self, filename):
        """提取原始文件名（去除分割后缀）"""
        try:
            # 移除扩展名
            name_without_ext = os.path.splitext(filename)[0]

            # 常见的分割模式
            patterns = ['_segment_', '_split_', '_part_']

            for pattern in patterns:
                if pattern in name_without_ext:
                    return name_without_ext.split(pattern)[0]

            # 如果没有明确的分割模式，检查是否以数字结尾
            parts = name_without_ext.split('_')
            if len(parts) > 1 and parts[-1].isdigit():
                return '_'.join(parts[:-1])

            return name_without_ext

        except Exception as e:
            self.main_window.log_message(f"提取原始文件名失败: {str(e)}", "ERROR")
            return filename

    def merge_group_results(self, original_name, group):
        """合并一组分割结果"""
        try:
            merged_items = []
            merged_supplier = ""
            merged_date = ""

            self.main_window.log_message(f"🔗 开始合并组 '{original_name}', 包含 {len(group)} 个文件")

            # 合并所有分割的商品数据
            for i, item in enumerate(group):
                result = item['result']
                filename = item['filename']
                
                self.main_window.log_message(f"  处理第{i+1}个文件: {filename}")

                # 🔥 修复：处理不同的数据结构格式
                ticket_data = None
                if isinstance(result, dict):
                    if result.get("success") and "data" in result:
                        ticket_data = result["data"]
                        self.main_window.log_message(f"    使用 result.data 格式")
                    elif "items" in result:
                        ticket_data = result
                        self.main_window.log_message(f"    使用 result 直接格式")

                if not ticket_data:
                    self.main_window.log_message(f"    ⚠️ 跳过无效数据格式", "WARNING")
                    continue

                # 获取供应商和日期信息（使用第一个非空值）
                if not merged_supplier and ticket_data.get('supplier'):
                    merged_supplier = ticket_data['supplier']
                    self.main_window.log_message(f"    供应商: {merged_supplier}")
                if not merged_date and ticket_data.get('date'):
                    merged_date = ticket_data['date']
                    self.main_window.log_message(f"    日期: {merged_date}")

                # 合并商品列表
                items = ticket_data.get('items', [])
                if isinstance(items, list):
                    self.main_window.log_message(f"    添加 {len(items)} 个商品")
                    merged_items.extend(items)
                else:
                    self.main_window.log_message(f"    ⚠️ 商品列表格式错误: {type(items)}", "WARNING")

            self.main_window.log_message(f"📊 合并前总商品数: {len(merged_items)}")

            # 去重处理（基于款号和颜色规格）
            unique_items = self.remove_duplicate_items(merged_items)

            self.main_window.log_message(f"📊 去重后商品数: {len(unique_items)}")

            # 创建合并后的结果
            merged_result = {
                'supplier': merged_supplier,
                'date': merged_date,
                'items': unique_items,
                'merged_from': [item['filename'] for item in group],
                'total_segments': len(group)
            }

            self.main_window.log_message(f"✅ 合并组 '{original_name}' 完成: {len(unique_items)} 个商品")
            return merged_result

        except Exception as e:
            import traceback
            self.main_window.log_message(f"合并组结果失败: {str(e)}", "ERROR")
            self.main_window.log_message(f"错误详情: {traceback.format_exc()}", "ERROR")
            return None

    def remove_duplicate_items(self, items):
        """去除重复的商品项（修复版：正确合并数量，避免数据丢失）"""
        try:
            unique_items = []
            seen_items = {}  # 改用字典存储，便于合并数量

            for item in items:
                # 创建唯一标识（只使用款号，不使用颜色规格避免过度去重）
                name = str(item.get("款号", item.get("name", item.get("product_code", ""))))
                spec = str(item.get("颜色规格", item.get("specification", item.get("color_spec", ""))))

                # 如果款号为空，直接添加（避免误删）
                if not name or name in ["", "None", "null"]:
                    unique_items.append(item.copy())
                    continue

                # 使用款号+颜色规格+单价+数量作为唯一标识，只有完全相同的商品才合并
                unit_price = str(item.get("单价", item.get("unit_price", item.get("price", ""))))
                quantity = str(item.get("数量", item.get("quantity", item.get("qty", ""))))
                item_key = f"{name}_{spec}_{unit_price}_{quantity}"

                if item_key not in seen_items:
                    # 第一次遇到，直接添加
                    seen_items[item_key] = len(unique_items)
                    unique_items.append(item.copy())  # 使用副本避免修改原数据
                else:
                    # 重复项，合并数量
                    existing_index = seen_items[item_key]
                    existing_item = unique_items[existing_index]

                    # 尝试合并数量
                    try:
                        existing_qty = float(existing_item.get("数量", existing_item.get("quantity", existing_item.get("qty", 0))))
                        new_qty = float(item.get("数量", item.get("quantity", item.get("qty", 0))))
                        merged_qty = existing_qty + new_qty

                        # 更新数量（保持原始数据类型）
                        if "数量" in existing_item:
                            existing_item["数量"] = merged_qty  # 保持数字类型
                        elif "quantity" in existing_item:
                            existing_item["quantity"] = merged_qty
                        elif "qty" in existing_item:
                            existing_item["qty"] = merged_qty

                    except Exception as e:
                        # 数量合并失败，作为新项目添加，避免数据丢失
                        self.main_window.log_message(f"⚠️ 无法合并商品数量: {name} - {spec}，作为独立项目保留", "WARNING")
                        unique_items.append(item.copy())

            # 只在有重复项时才显示去重日志
            if len(items) != len(unique_items):
                self.main_window.log_message(f"🔄 数据去重：{len(items)} → {len(unique_items)} 项")

            return unique_items

        except Exception as e:
            self.main_window.log_message(f"去重处理失败: {str(e)}", "ERROR")
            return items

    def add_merged_result_to_table(self, original_name, merged_result):
        """添加合并结果到商品明细表格（保持原有接口）"""
        # 这个方法暂时保留以维持兼容性
        self.add_merged_result_to_result_table(original_name, merged_result)

    def add_merged_result_to_result_table(self, original_name, merged_result):
        """添加合并结果到解析结果表格"""
        try:
            from PyQt5.QtWidgets import QTableWidgetItem, QWidget, QHBoxLayout, QPushButton
            from PyQt5.QtGui import QColor
            
            result_table = getattr(self.main_window, 'result_table', None)
            if not result_table:
                return None

            row = result_table.rowCount()
            result_table.insertRow(row)

            # 序号
            result_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))

            # 文件名（标记为合并结果）
            merged_filename = f"[合并] {original_name}"
            result_table.setItem(row, 1, QTableWidgetItem(merged_filename))

            # 供应商
            supplier = merged_result.get("supplier", "未知")
            result_table.setItem(row, 2, QTableWidgetItem(supplier))

            # 计算统计信息
            items = merged_result.get("items", [])
            quantity_sum = 0
            total_sum = 0

            self.main_window.log_message(f"📊 合并结果统计: {len(items)} 个商品")

            if isinstance(items, list):
                for i, item in enumerate(items):
                    try:
                        quantity = float(item.get("数量", 0))
                        total = float(item.get("小计", 0))
                        quantity_sum += quantity
                        total_sum += total
                        self.main_window.log_message(f"  第{i+1}个商品: {item.get('款号', '未知')} - 数量:{quantity}, 小计:{total}")
                    except Exception as ex:
                        self.main_window.log_message(f"  第{i+1}个商品数据解析失败: {ex}")

            self.main_window.log_message(f"📈 总计: 数量={quantity_sum}, 金额={total_sum}")

            # 数量总和
            result_table.setItem(row, 3, QTableWidgetItem(str(int(quantity_sum))))

            # 小计总和
            result_table.setItem(row, 4, QTableWidgetItem(f"¥{total_sum:.2f}"))

            # 类型
            ticket_type = "合并结果"
            result_table.setItem(row, 5, QTableWidgetItem(ticket_type))

            # 日期
            date = merged_result.get("date", "未知")
            result_table.setItem(row, 6, QTableWidgetItem(date))

            # 删除状态列，直接添加操作按钮到第7列
            # 🔥 操作按钮（删除和前缀）- 为合并结果添加完整的按钮组
            button_widget = QWidget()
            button_layout = QHBoxLayout(button_widget)
            button_layout.setContentsMargins(2, 2, 2, 2)
            button_layout.setSpacing(3)

            # 🔥 删除按钮 - 红色样式
            delete_btn = QPushButton("删除")
            delete_btn.setMaximumWidth(60)
            delete_btn.setMinimumHeight(28)
            delete_btn.setToolTip("删除此合并结果")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                }
                QPushButton:hover {
                    background-color: #c82333;
                }
                QPushButton:pressed {
                    background-color: #bd2130;
                }
            """)
            # 🔥 修复：使用按钮的父组件来动态获取当前行号，避免索引错误
            delete_btn.clicked.connect(lambda checked, btn=delete_btn: self.delete_result_row_by_button(btn))
            button_layout.addWidget(delete_btn)

            # 🔥 添加供应商前缀按钮 - 之前合并结果缺少此按钮
            prefix_btn = QPushButton("前缀")
            prefix_btn.setMaximumWidth(60)
            prefix_btn.setMinimumHeight(28)
            prefix_btn.setToolTip("为合并结果中的款号添加供应商前缀")
            prefix_btn.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                }
                QPushButton:pressed {
                    background-color: #004085;
                }
            """)
            # 创建合并文件的伪造路径用于前缀添加
            merged_file_key = f"[MERGED]_{original_name}"
            prefix_btn.clicked.connect(lambda checked, r=row, fp=merged_file_key, td=merged_result: self.add_prefix_to_row(r, fp, td))
            button_layout.addWidget(prefix_btn)

            result_table.setCellWidget(row, 7, button_widget)  # 操作列索引从8改为7

            # 存储合并结果数据用于后续处理
            if not hasattr(self.main_window, 'stored_results'):
                self.main_window.stored_results = {}
            
            # 🔥 关键修复：使用正确的数据结构存储合并结果
            merged_file_key = f"[MERGED]_{original_name}"
            formatted_result = {
                "success": True,
                "data": merged_result
            }
            self.main_window.stored_results[merged_file_key] = formatted_result

            self.main_window.log_message(f"✅ 添加合并结果到解析结果表格: {original_name} (第{row+1}行)")
            return row

        except Exception as e:
            import traceback
            self.main_window.log_message(f"添加合并结果失败: {str(e)}", "ERROR")
            self.main_window.log_message(f"错误详情: {traceback.format_exc()}", "ERROR")
            return None

    def check_and_enable_merge_button(self):
        """检查是否有分割结果并启用合并按钮"""
        try:
            # 从解析结果表格检查分割文件
            result_table = getattr(self.main_window, 'result_table', None)
            if not result_table or result_table.rowCount() == 0:
                return

            # 收集所有解析结果的文件名
            split_results = []
            file_groups = {}  # 按原始文件名分组

            for row in range(result_table.rowCount()):
                filename_item = result_table.item(row, 1)  # 文件名列
                if not filename_item:
                    continue

                filename = filename_item.text()
                
                # 更宽松的分割文件检测条件
                is_split_file = False

                # 检查常见的分割模式
                if ('_segment_' in filename or '_split_' in filename or
                    '_part_' in filename or filename.endswith(('_1', '_2', '_3', '_4', '_5')) or
                    filename.count('_') >= 2):
                    is_split_file = True

                # 检查数字后缀模式（如 filename_1, filename_2）
                parts = filename.split('_')
                if len(parts) > 1 and parts[-1].isdigit():
                    is_split_file = True

                if is_split_file:
                    split_results.append(filename)

                    # 按原始文件名分组
                    original_name = self.extract_original_filename(filename)
                    if original_name not in file_groups:
                        file_groups[original_name] = []
                    file_groups[original_name].append(filename)

            # 检查是否有可合并的组（每组至少2个文件）
            mergeable_groups = {name: group for name, group in file_groups.items() if len(group) >= 2}

            # 如果有可合并的分割结果，启用合并按钮
            if len(split_results) >= 2 and mergeable_groups:
                if hasattr(self.main_window, 'merge_tables_btn'):
                    self.main_window.merge_tables_btn.setEnabled(True)
                    self.main_window.log_message(f"检测到 {len(mergeable_groups)} 组可合并的分割结果（共{len(split_results)}个文件）")
            else:
                # 如果没有分割结果，但有多个文件，也可以尝试合并
                if result_table.rowCount() >= 2:
                    if hasattr(self.main_window, 'merge_tables_btn'):
                        self.main_window.merge_tables_btn.setEnabled(True)
                        self.main_window.log_message(f"检测到 {result_table.rowCount()} 个解析结果，可以尝试合并")

        except Exception as e:
            self.main_window.log_message(f"检查合并条件失败: {str(e)}", "ERROR")

    def on_result_selection_changed(self):
        """解析结果选择变更事件"""
        try:
            # 获取result_table的引用
            result_table = getattr(self.main_window, 'result_table', None)
            if not result_table:
                return
                
            selected_rows = result_table.selectionModel().selectedRows()
            if selected_rows:
                row = selected_rows[0].row()
                self.main_window.log_message(f"选中解析结果: 第{row + 1}行")
                
                # 🔥 新增：同步缩略图选择
                file_name_item = result_table.item(row, 1)  # 文件名列
                if file_name_item:
                    file_name = file_name_item.text()
                    file_path = self.main_window.find_thumbnail_by_file_name(file_name)
                    if file_path:
                        self.main_window.sync_thumbnail_selection(file_path)
                
                # 🔥 手动加载选中的解析结果到商品明细表格
                self.load_selected_result_to_table(row)
        except Exception as e:
            self.main_window.log_message(f"解析结果选择事件处理失败: {str(e)}", "ERROR")

    def load_selected_result_to_table(self, row):
        """将选中的解析结果加载到商品明细表格"""
        try:
            # 获取result_table的引用
            result_table = getattr(self.main_window, 'result_table', None)
            if not result_table or row >= result_table.rowCount():
                return

            # 获取文件名
            file_name_item = result_table.item(row, 1)
            if not file_name_item:
                return
                
            file_name = file_name_item.text()
            
            # 从存储的结果数据中查找对应的解析结果
            if not hasattr(self.main_window, 'stored_results'):
                self.main_window.stored_results = {}
            
            # 查找匹配的结果数据
            result_data = None
            target_file_path = None
            
            self.main_window.log_message(f"🔍 查找文件: {file_name}")
            
            for stored_file_path, stored_result in self.main_window.stored_results.items():
                # 🔥 修复：处理合并结果的特殊情况
                if file_name.startswith("[合并]") and stored_file_path.startswith("[MERGED]"):
                    # 提取合并结果的原始文件名进行匹配
                    merged_name = file_name.replace("[合并] ", "")
                    stored_merged_name = stored_file_path.replace("[MERGED]_", "")
                    self.main_window.log_message(f"  检查合并文件: {merged_name} vs {stored_merged_name}")
                    if merged_name == stored_merged_name:
                        result_data = stored_result
                        target_file_path = stored_file_path
                        self.main_window.log_message(f"  ✅ 找到合并结果匹配")
                        break
                else:
                    # 普通文件的匹配逻辑
                    stored_display_name = self.main_window.get_display_name_for_file(stored_file_path)
                    self.main_window.log_message(f"  检查普通文件: {file_name} vs {stored_display_name}")
                    if stored_display_name == file_name:
                        result_data = stored_result
                        target_file_path = stored_file_path
                        self.main_window.log_message(f"  ✅ 找到普通文件匹配")
                        break
            
            if result_data and target_file_path:
                # 🔥 修复：保存当前表格的ERP状态，使用明确的文件路径
                current_file = self.current_table_file_path
                if current_file:
                    self.save_current_erp_state(current_file)
                
                # 清空当前商品明细表格
                self.clear_all_tables()
                
                # 将解析结果添加到商品明细表格
                self.add_result_to_table(target_file_path, result_data)
                
                # 🔥 新增：恢复目标文件的ERP状态
                self.restore_erp_state_for_file(target_file_path)
                
                self.main_window.log_message(f"已加载解析结果到商品明细表格: {file_name}")
            else:
                self.main_window.log_message(f"未找到对应的解析结果数据: {file_name}", "WARNING")
                
        except Exception as e:
            self.main_window.log_message(f"加载解析结果失败: {str(e)}", "ERROR")

    def save_current_erp_state(self, file_path=None):
        """保存当前文件的ERP查询状态 - 基于商品唯一标识而非行号"""
        try:
            # 🔥 修复：优先使用传入的文件路径，然后是current_table_file_path，最后是current_selected_file
            current_file = file_path
            if not current_file:
                current_file = self.current_table_file_path
            if not current_file:
                current_file = getattr(self.main_window, 'current_selected_file', None)
            if not current_file:
                # 尝试从图像管理器获取当前图像
                if hasattr(self.main_window, 'image_manager'):
                    current_file = self.main_window.image_manager.current_image_path

            if not current_file:
                print(f"⚠️ 无法确定当前文件路径，跳过ERP状态保存")
                return

            # 初始化ERP状态存储
            if not hasattr(self.main_window, 'erp_states_cache'):
                self.main_window.erp_states_cache = {}

            # 获取当前表格和ERP匹配结果
            current_table = self.get_current_table()
            erp_results = getattr(self.main_window, 'erp_match_results', {})
            
            if not current_table or not erp_results:
                print(f"💾 文件 {os.path.basename(current_file)} 没有需要保存的ERP状态")
                return

            # 🔥 核心修复：基于商品唯一标识保存ERP状态
            sku_based_erp_state = {}
            
            for row, match_result in erp_results.items():
                try:
                    if row < current_table.rowCount():
                        # 获取商品唯一标识：款号 + 颜色规格
                        sku_item = current_table.item(row, 1)  # 款号列
                        color_item = current_table.item(row, 2)  # 颜色规格列
                        
                        if sku_item and color_item:
                            sku_code = sku_item.text().strip()
                            color_spec = color_item.text().strip()
                            
                            # 创建商品唯一标识
                            product_key = f"{sku_code}|{color_spec}"
                            
                            # 保存ERP状态，同时记录原始行号（用于调试）
                            enhanced_match_result = match_result.copy()
                            enhanced_match_result['original_row'] = row
                            enhanced_match_result['sku_code'] = sku_code
                            enhanced_match_result['color_spec'] = color_spec
                            enhanced_match_result['product_key'] = product_key
                            
                            sku_based_erp_state[product_key] = enhanced_match_result
                            
                            print(f"💾 保存商品ERP状态: {product_key} (原行号{row+1}) → 文件 {os.path.basename(current_file)}")
                        else:
                            print(f"⚠️ 第{row+1}行缺少款号或颜色规格信息，跳过保存")
                            
                except Exception as row_error:
                    print(f"❌ 保存第{row+1}行ERP状态失败: {str(row_error)}")
                    continue

            if sku_based_erp_state:
                self.main_window.erp_states_cache[current_file] = {
                    'sku_based_erp_state': sku_based_erp_state,  # 🔥 新格式：基于商品唯一标识
                    'timestamp': time.time(),
                    'file_path': current_file
                }
                print(f"✅ 已保存文件 {os.path.basename(current_file)} 的ERP状态，包含 {len(sku_based_erp_state)} 个商品")
                self.main_window.log_message(f"已保存ERP状态: {len(sku_based_erp_state)} 个商品 → {os.path.basename(current_file)}", "INFO")
            else:
                print(f"⚠️ 文件 {os.path.basename(current_file)} 没有有效的ERP状态需要保存")

        except Exception as e:
            print(f"❌ 保存ERP状态失败: {str(e)}")
            import traceback
            print(f"❌ 错误详情: {traceback.format_exc()}")

    def restore_erp_state_for_file(self, file_path):
        """恢复指定文件的ERP查询状态 - 基于商品唯一标识匹配"""
        try:
            # 检查是否有保存的ERP状态
            if not hasattr(self.main_window, 'erp_states_cache'):
                self.main_window.erp_states_cache = {}

            # 🔥 核心修复：先清空当前ERP状态，确保状态隔离
            if not hasattr(self.main_window, 'erp_match_results'):
                self.main_window.erp_match_results = {}
            
            print(f"🧹 清空当前ERP状态，准备切换到文件: {os.path.basename(file_path)}")
            self.main_window.erp_match_results.clear()

            # 检查目标文件是否有保存的状态
            if file_path not in self.main_window.erp_states_cache:
                print(f"🔄 文件 {os.path.basename(file_path)} 没有保存的ERP状态，显示空状态")
                # 更新表格显示为空状态
                self.restore_table_row_status_from_erp_results()
                return

            saved_state = self.main_window.erp_states_cache[file_path]
            
            # 🔥 核心修复：检查保存格式并进行兼容性处理
            sku_based_erp_state = saved_state.get('sku_based_erp_state', {})
            
            # 兼容旧格式（基于行号）- 但不直接恢复，避免污染
            legacy_erp_results = saved_state.get('erp_match_results', {})
            if legacy_erp_results and not sku_based_erp_state:
                print(f"⚠️ 检测到旧格式的ERP状态，为避免状态污染，显示空状态")
                print(f"   建议重新查询该文件的ERP状态以获得最新的保存格式")
                # 更新表格显示为空状态
                self.restore_table_row_status_from_erp_results()
                return

            if not sku_based_erp_state:
                print(f"🔄 文件 {os.path.basename(file_path)} 没有有效的ERP状态数据，显示空状态")
                # 更新表格显示为空状态
                self.restore_table_row_status_from_erp_results()
                return

            # 获取当前表格
            current_table = self.get_current_table()
            if not current_table:
                print(f"❌ 无法获取当前表格，无法恢复ERP状态")
                return

            print(f"🔄 开始恢复文件 {os.path.basename(file_path)} 的ERP状态...")
            
            # 🔥 核心修复：基于商品唯一标识匹配和恢复ERP状态
            restored_count = 0
            row_based_erp_results = {}  # 重建基于行号的ERP结果用于系统兼容
            
            for row in range(current_table.rowCount()):
                try:
                    # 获取当前行的商品唯一标识
                    sku_item = current_table.item(row, 1)  # 款号列
                    color_item = current_table.item(row, 2)  # 颜色规格列
                    
                    if sku_item and color_item:
                        sku_code = sku_item.text().strip()
                        color_spec = color_item.text().strip()
                        product_key = f"{sku_code}|{color_spec}"
                        
                        # 查找匹配的ERP状态
                        if product_key in sku_based_erp_state:
                            match_result = sku_based_erp_state[product_key]
                            
                            # 恢复到基于行号的格式（系统兼容性）
                            row_based_erp_results[row] = match_result
                            restored_count += 1
                            
                            print(f"✅ 恢复商品ERP状态: {product_key} → 第{row+1}行")
                        else:
                            print(f"ℹ️ 第{row+1}行商品 {product_key} 没有保存的ERP状态")
                    else:
                        print(f"⚠️ 第{row+1}行缺少款号或颜色规格信息，跳过恢复")
                        
                except Exception as row_error:
                    print(f"❌ 恢复第{row+1}行ERP状态失败: {str(row_error)}")
                    continue

            # 🔥 核心修复：更新系统的ERP匹配结果（无论是否有恢复数据）
            if restored_count > 0:
                self.main_window.erp_match_results.update(row_based_erp_results)
                print(f"🎉 成功恢复文件 {os.path.basename(file_path)} 的ERP状态: {restored_count} 个商品")
                self.main_window.log_message(f"已恢复ERP状态: {restored_count} 个商品", "SUCCESS")
            else:
                print(f"ℹ️ 文件 {os.path.basename(file_path)} 没有匹配的商品需要恢复ERP状态，显示空状态")

            # 🔥 核心修复：无论是否有恢复数据，都要更新表格显示
            self.restore_table_row_status_from_erp_results()

        except Exception as e:
            print(f"❌ 恢复ERP状态失败: {str(e)}")
            import traceback
            print(f"❌ 错误详情: {traceback.format_exc()}")
            # 🔥 修复：出错时清空ERP结果，显示空状态
            if hasattr(self.main_window, 'erp_match_results'):
                self.main_window.erp_match_results.clear()
            self.restore_table_row_status_from_erp_results()
            print(f"⚠️ 恢复失败，已清空ERP状态")

    def restore_table_row_status_from_erp_results(self):
        """根据ERP查询结果恢复表格行状态"""
        try:
            if not hasattr(self.main_window, 'erp_match_results') or not self.main_window.erp_match_results:
                return

            # 获取当前表格
            current_table = self.get_current_table()
            if not current_table:
                return

            # 遍历ERP匹配结果，更新对应行的状态
            for row, match_result in self.main_window.erp_match_results.items():
                try:
                    if row < current_table.rowCount():
                        # 提取状态信息
                        saved_status = match_result.get('status', '')
                        color_groups = match_result.get('color_groups', {})
                        confirmed = match_result.get('confirmed', False)
                        
                        # 🔥 核心修复：正确处理确认后的状态
                        # 1. 优先使用保存的状态（包括确认后的状态）
                        if saved_status and confirmed:
                            # 已确认且有保存状态，使用确认后的状态（如 "📈 上涨"、"📉 下降"）
                            erp_status = saved_status
                            print(f"🎯 恢复确认后状态: {saved_status}")
                        elif saved_status:
                            # 有保存状态但未确认，使用保存的状态
                            erp_status = saved_status
                            print(f"🔄 恢复保存状态: {saved_status}")
                        elif color_groups:
                            # 没有保存状态但有颜色分组，显示待确认状态
                            erp_status = "🟡待确认"
                            print(f"❓ 设置待确认状态（有颜色分组）")
                        else:
                            # 默认已查询状态
                            erp_status = "✅ 已查询"
                            print(f"✅ 设置默认查询状态")

                        # 更新表格行状态
                        self.update_table_row_status(current_table, row, erp_status=erp_status)
                        print(f"✅ 恢复第{row+1}行状态: {erp_status}")
                        
                        # 🔥 核心修复：恢复利润数据
                        profit_text = match_result.get('profit_text', '')
                        if profit_text and confirmed:
                            # 恢复利润列（第7列）
                            profit_item = QTableWidgetItem(profit_text)
                            profit_item.setTextAlignment(Qt.AlignCenter)
                            
                            # 根据保存的利润值设置颜色
                            profit_value = match_result.get('profit_value', 0)
                            if profit_value < 20:
                                profit_item.setForeground(QColor(244, 67, 54))  # 红色
                            else:
                                profit_item.setForeground(QColor(76, 175, 80))  # 绿色
                                
                            current_table.setItem(row, 7, profit_item)
                            print(f"💰 恢复第{row+1}行利润: {profit_text}")

                        # 🔥 新增：恢复退货率数据
                        return_rate_text = match_result.get('return_rate_text', '')
                        if return_rate_text and return_rate_text != "-":
                            # 恢复退货率列（第8列）
                            return_rate_item = QTableWidgetItem(return_rate_text)
                            return_rate_item.setTextAlignment(Qt.AlignCenter)
                            return_rate_item.setFlags(return_rate_item.flags() & ~Qt.ItemIsEditable)

                            # 根据保存的退货率设置颜色
                            return_rates = match_result.get('return_rates', {})
                            if return_rates:
                                from modules.return_rate_calculator import ReturnRateCalculator
                                from PyQt5.QtGui import QColor
                                calculator = ReturnRateCalculator()
                                summary = calculator.get_return_rate_summary(return_rates)
                                return_rate_item.setForeground(QColor(summary["color"]))

                            # 更新为三列格式：尝试解析旧格式并转换为新格式
                            try:
                                if "7天:" in return_rate_text and "15天:" in return_rate_text and "30天:" in return_rate_text:
                                    # 解析格式如 "7天:82.9% | 15天:75.5% | 30天:68.2%"
                                    parts = return_rate_text.split(" | ")
                                    rates = {}
                                    for part in parts:
                                        if ":" in part and "%" in part:
                                            period = part.split(":")[0]
                                            rate_str = part.split(":")[1].replace("%", "")
                                            rates[period] = float(rate_str)

                                    # 使用新的三列格式设置
                                    formatted_rates = calculator.format_return_rate_integer(rates)
                                    periods = ["7天", "15天", "30天"]
                                    for j, period in enumerate(periods):
                                        col_index = 8 + j  # 第8、9、10列
                                        rate_text_new = formatted_rates[period]

                                        return_rate_item_new = QTableWidgetItem(rate_text_new)
                                        return_rate_item_new.setTextAlignment(Qt.AlignCenter)
                                        return_rate_item_new.setFlags(return_rate_item_new.flags() & ~Qt.ItemIsEditable)

                                        # 设置颜色
                                        if rate_text_new != "-":
                                            try:
                                                rate_value = float(rate_text_new.replace("%", ""))
                                                if rate_value < 20:
                                                    color = "#4CAF50"  # 绿色
                                                elif rate_value < 50:
                                                    color = "#FF9800"  # 橙色
                                                else:
                                                    color = "#F44336"  # 红色
                                                return_rate_item_new.setForeground(QColor(color))
                                            except:
                                                pass

                                        current_table.setItem(row, col_index, return_rate_item_new)

                                    print(f"📊 恢复第{row+1}行退货率(三列): 7退:{formatted_rates['7天']} 15退:{formatted_rates['15天']} 30退:{formatted_rates['30天']}")
                                else:
                                    # 如果不是标准格式，设置为旧的单列格式（兼容性）
                                    current_table.setItem(row, 8, return_rate_item)
                                    print(f"📊 恢复第{row+1}行退货率(旧格式): {return_rate_text}")
                            except:
                                # 解析失败，使用旧格式
                                current_table.setItem(row, 8, return_rate_item)
                                print(f"📊 恢复第{row+1}行退货率(解析失败): {return_rate_text}")

                except Exception as row_error:
                    print(f"❌ 恢复第{row+1}行状态失败: {str(row_error)}")
                    continue

            print(f"✅ 已恢复 {len(self.main_window.erp_match_results)} 行的状态显示")

        except Exception as e:
            print(f"❌ 恢复表格行状态失败: {str(e)}")
            import traceback
            print(f"❌ 错误详情: {traceback.format_exc()}")

    def add_file_result_to_table(self, file_path, result):
        """添加文件解析结果到解析列表表格"""
        try:
            # 获取result_table的引用
            result_table = getattr(self.main_window, 'result_table', None)
            if not result_table:
                self.main_window.log_message("解析结果表格未初始化", "ERROR")
                return

            # 🔥 存储解析结果数据，用于后续手动加载
            if not hasattr(self.main_window, 'stored_results'):
                self.main_window.stored_results = {}
            self.main_window.stored_results[file_path] = result

            # 提取结果信息
            if isinstance(result, dict) and result.get("success") and "data" in result:
                ticket_data = result["data"]
            elif isinstance(result, dict) and "items" in result:
                ticket_data = result
            else:
                return

            # 计算统计信息
            items = ticket_data.get("items", [])
            total_quantity = sum(float(item.get("数量", 0)) for item in items)
            total_amount = sum(float(item.get("小计", 0)) for item in items)

            # 添加到解析结果表格
            row = result_table.rowCount()
            result_table.insertRow(row)

            # 序号
            result_table.setItem(row, 0, QTableWidgetItem(str(row + 1)))

            # 文件名 - 使用重命名后的显示名称
            display_name = self.main_window.get_display_name_for_file(file_path)
            result_table.setItem(row, 1, QTableWidgetItem(display_name))

            # 供应商
            supplier = ticket_data.get("supplier", "未知")
            result_table.setItem(row, 2, QTableWidgetItem(supplier))

            # 数量总和
            result_table.setItem(row, 3, QTableWidgetItem(str(int(total_quantity))))

            # 小计总和
            result_table.setItem(row, 4, QTableWidgetItem(f"¥{total_amount:.2f}"))

            # 类型
            ticket_type = "取货" if total_quantity >= 0 else "退货"
            result_table.setItem(row, 5, QTableWidgetItem(ticket_type))

            # 日期
            date = ticket_data.get("date", "未知")
            result_table.setItem(row, 6, QTableWidgetItem(date))

            # 删除状态列，直接添加操作按钮到第7列
            # 操作按钮（删除和前缀）
            button_widget = QWidget()
            button_layout = QHBoxLayout(button_widget)
            button_layout.setContentsMargins(2, 2, 2, 2)
            button_layout.setSpacing(3)

            # 🔥 删除按钮 - 改为红色样式，更清晰的文字
            delete_btn = QPushButton("删除")
            delete_btn.setMaximumWidth(60)
            delete_btn.setMinimumHeight(28)
            delete_btn.setToolTip("删除此解析结果")
            delete_btn.setStyleSheet("""
                QPushButton {
                    background-color: #dc3545;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                }
                QPushButton:hover {
                    background-color: #c82333;
                    transform: translateY(-1px);
                }
                QPushButton:pressed {
                    background-color: #bd2130;
                    transform: translateY(0px);
                }
            """)
            # 🔥 修复：使用按钮的父组件来动态获取当前行号，避免索引错误
            delete_btn.clicked.connect(lambda checked, btn=delete_btn: self.delete_result_row_by_button(btn))
            button_layout.addWidget(delete_btn)

            # 🔥 供应商前缀按钮 - 蓝色样式，更清晰的文字
            prefix_btn = QPushButton("前缀")
            prefix_btn.setMaximumWidth(60)
            prefix_btn.setMinimumHeight(28)
            prefix_btn.setToolTip("为款号添加供应商前缀")
            prefix_btn.setStyleSheet("""
                QPushButton {
                    background-color: #007bff;
                    color: white;
                    border: none;
                    padding: 4px 8px;
                    border-radius: 4px;
                    font-weight: bold;
                    font-size: 11px;
                    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                }
                QPushButton:hover {
                    background-color: #0056b3;
                    transform: translateY(-1px);
                }
                QPushButton:pressed {
                    background-color: #004085;
                    transform: translateY(0px);
                }
            """)
            prefix_btn.clicked.connect(lambda checked, r=row, fp=file_path, td=ticket_data: self.add_prefix_to_row(r, fp, td))
            button_layout.addWidget(prefix_btn)

            result_table.setCellWidget(row, 7, button_widget)  # 操作列索引从8改为7

            # 检查并启用合并按钮
            self.check_and_enable_merge_button()

        except Exception as e:
            self.main_window.log_message(f"添加解析结果失败: {str(e)}", "ERROR")

    def delete_result_row_by_button(self, button):
        """通过按钮动态获取行号并删除解析结果行"""
        try:
            # 获取result_table的引用
            result_table = getattr(self.main_window, 'result_table', None)
            if not result_table:
                self.main_window.log_message("解析结果表格未初始化", "ERROR")
                return

            # 🔥 修复：动态获取按钮所在的行号
            row = -1
            for i in range(result_table.rowCount()):
                cell_widget = result_table.cellWidget(i, 7)  # 操作列
                if cell_widget:
                    # 查找按钮是否在这个cell_widget中
                    layout = cell_widget.layout()
                    if layout:
                        for j in range(layout.count()):
                            widget = layout.itemAt(j).widget()
                            if widget == button:
                                row = i
                                break
                    if row != -1:
                        break

            if row == -1:
                self.main_window.log_message("无法找到按钮对应的行", "ERROR")
                return

            print(f"🗑️ 删除按钮点击：动态获取到行号 {row}")

            # 调用原有的删除方法
            self.delete_result_row(row)

        except Exception as e:
            self.main_window.log_message(f"删除解析结果失败: {str(e)}", "ERROR")

    def delete_result_row(self, row):
        """删除解析结果行"""
        try:
            # 获取result_table的引用
            result_table = getattr(self.main_window, 'result_table', None)
            if not result_table:
                self.main_window.log_message("解析结果表格未初始化", "ERROR")
                return

            # 🔥 重要修复：删除解析结果时清空商品明细表格
            self.clear_all_tables()

            # 删除解析结果行
            result_table.removeRow(row)

            # 重新编号
            for i in range(result_table.rowCount()):
                result_table.setItem(i, 0, QTableWidgetItem(str(i + 1)))

            # 清空ERP匹配结果缓存
            if hasattr(self.main_window, 'erp_match_results'):
                self.main_window.erp_match_results.clear()

            self.main_window.log_message(f"已删除第{row + 1}行解析结果")
        except Exception as e:
            self.main_window.log_message(f"删除解析结果失败: {str(e)}", "ERROR")

    def clear_all_tables(self):
        """清空所有商品明细表格"""
        try:
            if hasattr(self, 'pickup_table'):
                self.pickup_table.setRowCount(0)
            if hasattr(self, 'return_table'):
                self.return_table.setRowCount(0)
            self.main_window.log_message("已清空商品明细表格")
        except Exception as e:
            self.main_window.log_message(f"清空表格失败: {str(e)}", "ERROR")

    def add_prefix_to_row(self, row, file_path, ticket_data):
        """为指定行的款号添加供应商前缀"""
        try:
            # 🔥 判断是否为合并结果
            is_merged = file_path.startswith("[MERGED]_")
            
            # 获取文件显示名称用于日志
            if is_merged:
                display_name = f"合并结果: {file_path[9:]}"  # 去掉 "[MERGED]_" 前缀
            else:
                display_name = self.main_window.get_display_name_for_file(file_path)
            
            self.main_window.log_message(f"🏷️ 开始为 '{display_name}' 添加供应商前缀...")

            # 智能提取供应商代码
            supplier_code = self.smart_extract_supplier_code(file_path, ticket_data)

            # 如果智能提取失败，提供手动输入选项
            if not supplier_code:
                self.main_window.log_message(f"⚠️ 无法自动识别供应商代码，请手动输入", "WARNING")
                supplier_code = self.prompt_for_supplier_code(file_path)

            if not supplier_code:
                self.main_window.log_message("❌ 未提供供应商代码，操作取消", "WARNING")
                return

            self.main_window.log_message(f"✅ 识别到供应商代码: {supplier_code}")

            # 为所有商品添加前缀
            items = ticket_data.get("items", [])
            if not items:
                self.main_window.log_message("❌ 该记录没有商品明细", "WARNING")
                return

            updated_count = 0
            skipped_count = 0

            for i, item in enumerate(items):
                product_code = item.get("款号", "")
                if product_code:
                    if not product_code.startswith(f"{supplier_code}-"):
                        # 添加前缀
                        old_code = product_code
                        item["款号"] = f"{supplier_code}-{product_code}"
                        updated_count += 1
                        self.main_window.log_message(f"  第{i+1}个商品: {old_code} → {item['款号']}")
                    else:
                        skipped_count += 1
                        self.main_window.log_message(f"  第{i+1}个商品: {product_code} (已有前缀，跳过)")

            if updated_count > 0:
                # 更新供应商信息
                ticket_data["supplier"] = supplier_code

                # 🔥 获取result_table的引用
                result_table = getattr(self.main_window, 'result_table', None)
                if result_table and row < result_table.rowCount():
                    # 更新表格显示中的供应商列
                    result_table.setItem(row, 2, QTableWidgetItem(supplier_code))

                # 🔥 更新存储的结果数据
                if is_merged:
                    # 对于合并结果，更新stored_results中的数据
                    if hasattr(self.main_window, 'stored_results') and file_path in self.main_window.stored_results:
                        self.main_window.stored_results[file_path]["data"] = ticket_data
                else:
                    # 对于普通结果，更新stored_results中的数据
                    if hasattr(self.main_window, 'stored_results') and file_path in self.main_window.stored_results:
                        if isinstance(self.main_window.stored_results[file_path], dict) and "data" in self.main_window.stored_results[file_path]:
                            self.main_window.stored_results[file_path]["data"] = ticket_data
                        else:
                            self.main_window.stored_results[file_path] = {"success": True, "data": ticket_data}

                # 强制刷新表格显示
                self.force_refresh_table_display(file_path, items)

                operation_type = "合并结果" if is_merged else "解析结果"
                self.main_window.log_message(f"🎉 {operation_type}前缀添加完成：{updated_count} 个商品添加前缀 {supplier_code}-，{skipped_count} 个跳过", "SUCCESS")
            else:
                self.main_window.log_message("ℹ️ 所有商品已有前缀，无需添加", "INFO")

        except Exception as e:
            self.main_window.log_message(f"❌ 添加前缀失败: {str(e)}", "ERROR")

    def smart_extract_supplier_code(self, file_path, ticket_data):
        """智能提取供应商代码"""
        try:
            # 已知的供应商代码列表
            supplier_codes = [
                "GT408", "GT251", "GT155", "GT253", "GT158", "GTAF05",
                "DSD258", "DSD129", "DSD106", "GD340"
            ]

            # 1. 优先从重命名后的文件名中提取
            display_name = self.main_window.get_display_name_for_file(file_path)
            extracted_code = self.extract_supplier_code(display_name, supplier_codes)
            if extracted_code:
                self.main_window.log_message(f"🎯 从文件名识别供应商代码: {extracted_code}")
                return extracted_code

            # 2. 从解析结果的供应商字段提取
            supplier = ticket_data.get("supplier", "")
            if supplier:
                extracted_code = self.extract_supplier_code(supplier, supplier_codes)
                if extracted_code:
                    self.main_window.log_message(f"🎯 从解析结果识别供应商代码: {extracted_code}")
                    return extracted_code

            self.main_window.log_message(f"⚠️ 无法从文件名 '{display_name}' 识别供应商代码", "WARNING")
            return None

        except Exception as e:
            self.main_window.log_message(f"智能提取供应商代码失败: {str(e)}", "ERROR")
            return None

    def extract_supplier_code(self, text, supplier_codes):
        """从文本中提取供应商代码"""
        if not text:
            return None

        text_upper = text.upper()

        # 1. 精确匹配：检查文本是否以供应商代码开头
        for code in supplier_codes:
            if text_upper.startswith(code):
                return code

        # 2. 包含匹配：检查文本是否包含供应商代码
        for code in supplier_codes:
            if code in text_upper:
                return code

        return None

    def prompt_for_supplier_code(self, file_path):
        """提示用户手动输入供应商代码"""
        try:
            from PyQt5.QtWidgets import QInputDialog

            display_name = self.main_window.get_display_name_for_file(file_path)

            # 弹出输入对话框
            dialog = QInputDialog(self.main_window)
            dialog.setWindowTitle("输入供应商代码")
            dialog.setLabelText(f"无法自动识别文件 '{display_name}' 的供应商代码\n\n请手动输入供应商代码（如：GTAF05、GT408等）:")
            dialog.setTextValue("")

            # 设置深色主题样式
            dialog.setStyleSheet("""
                QInputDialog {
                    background-color: #2b2b2b;
                    color: #ffffff;
                }
                QLabel {
                    color: #ffffff;
                    font-size: 12px;
                }
                QLineEdit {
                    background-color: #3c3c3c;
                    border: 1px solid #555555;
                    color: #ffffff;
                    padding: 5px;
                    font-size: 12px;
                }
                QPushButton {
                    background-color: #0078d4;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    font-size: 12px;
                }
                QPushButton:hover {
                    background-color: #106ebe;
                }
            """)

            ok = dialog.exec_()
            supplier_code = dialog.textValue()

            if ok and supplier_code.strip():
                supplier_code = supplier_code.strip().upper()
                # 验证输入的供应商代码格式
                if self.validate_supplier_code(supplier_code):
                    self.main_window.log_message(f"✅ 手动输入供应商代码: {supplier_code}")
                    return supplier_code
                else:
                    self.main_window.log_message(f"❌ 供应商代码格式无效: {supplier_code}", "ERROR")
                    return None
            else:
                self.main_window.log_message("用户取消输入供应商代码")
                return None

        except Exception as e:
            self.main_window.log_message(f"输入供应商代码失败: {str(e)}", "ERROR")
            return None

    def validate_supplier_code(self, code):
        """验证供应商代码格式"""
        import re
        # 允许字母+数字组合，长度3-10位
        pattern = r'^[A-Z]{2,6}\d{2,4}$'
        return bool(re.match(pattern, code)) and 3 <= len(code) <= 10

    def force_refresh_table_display(self, file_path, items):
        """强制刷新表格显示"""
        try:
            # 清空当前表格
            self.clear_all_tables()

            # 重新添加商品到表格
            pickup_items = [item for item in items if float(item.get("数量", 0)) >= 0]
            return_items = [item for item in items if float(item.get("数量", 0)) < 0]

            if pickup_items:
                self.add_items_to_table(self.pickup_table, pickup_items, file_path)
            if return_items:
                self.add_items_to_table(self.return_table, return_items, file_path)

            self.main_window.log_message("表格显示已刷新")

        except Exception as e:
            self.main_window.log_message(f"刷新表格显示失败: {str(e)}", "ERROR")

    def add_new_row_to_table(self, table, table_type):
        """为指定表格添加新行"""
        try:
            # 获取当前行数
            current_row_count = table.rowCount()
            new_row_number = current_row_count + 1

            # 插入新行
            table.insertRow(current_row_count)

            # 设置行数（不可编辑）
            row_item = QTableWidgetItem(str(new_row_number))
            row_item.setTextAlignment(Qt.AlignCenter)
            row_item.setFlags(row_item.flags() & ~Qt.ItemIsEditable)
            table.setItem(current_row_count, 0, row_item)

            # 设置款号（可编辑）
            sku_item = QTableWidgetItem("")
            sku_item.setTextAlignment(Qt.AlignCenter)
            sku_item.setFlags(sku_item.flags() | Qt.ItemIsEditable)
            table.setItem(current_row_count, 1, sku_item)

            # 设置颜色规格（可编辑）
            spec_item = QTableWidgetItem("")
            spec_item.setTextAlignment(Qt.AlignCenter)
            spec_item.setFlags(spec_item.flags() | Qt.ItemIsEditable)
            table.setItem(current_row_count, 2, spec_item)

            # 设置数量（可编辑，默认值为1）
            quantity_item = QTableWidgetItem("1")
            quantity_item.setTextAlignment(Qt.AlignCenter)
            quantity_item.setFlags(quantity_item.flags() | Qt.ItemIsEditable)
            table.setItem(current_row_count, 3, quantity_item)

            # 设置单价（可编辑）
            price_item = QTableWidgetItem("")
            price_item.setTextAlignment(Qt.AlignCenter)
            price_item.setFlags(price_item.flags() | Qt.ItemIsEditable)
            table.setItem(current_row_count, 4, price_item)

            # 设置小计（可编辑）
            total_item = QTableWidgetItem("")
            total_item.setTextAlignment(Qt.AlignCenter)
            total_item.setFlags(total_item.flags() | Qt.ItemIsEditable)
            table.setItem(current_row_count, 5, total_item)

            # 设置状态（不可编辑）
            status_item = QTableWidgetItem("手动添加")
            status_item.setTextAlignment(Qt.AlignCenter)
            status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
            table.setItem(current_row_count, 6, status_item)

            # 设置利润（不可编辑）
            profit_item = QTableWidgetItem("未计算")
            profit_item.setTextAlignment(Qt.AlignCenter)
            profit_item.setFlags(profit_item.flags() & ~Qt.ItemIsEditable)
            table.setItem(current_row_count, 7, profit_item)

            # 设置退货率列（初始为空）
            return_rate_item = QTableWidgetItem("-")
            return_rate_item.setTextAlignment(Qt.AlignCenter)
            return_rate_item.setFlags(return_rate_item.flags() & ~Qt.ItemIsEditable)
            table.setItem(current_row_count, 8, return_rate_item)

            # 设置新行的背景色以区分手动添加的行
            for col in range(table.columnCount()):
                item = table.item(current_row_count, col)
                if item:
                    item.setBackground(QColor("#2d4a2d"))  # 深绿色背景

            # 滚动到新添加的行
            table.scrollToItem(table.item(current_row_count, 0))

            # 自动选中新行的款号列，方便用户输入
            table.setCurrentCell(current_row_count, 1)

            self.main_window.log_message(f"已添加新行到{table_type}表格，行号: {new_row_number}")

        except Exception as e:
            self.main_window.log_message(f"添加新行失败: {str(e)}", "ERROR")

    def on_table_item_changed(self, table, item):
        """表格项变更事件处理 - 自动计算小计"""
        try:
            row = item.row()
            col = item.column()

            # 只有当数量列（第3列）或单价列（第4列）变更时才计算小计
            if col in [3, 4]:  # 数量列或单价列
                quantity_item = table.item(row, 3)
                price_item = table.item(row, 4)
                total_item = table.item(row, 5)

                if quantity_item and price_item and total_item:
                    try:
                        # 获取数量和单价
                        quantity_text = quantity_item.text().strip()
                        price_text = price_item.text().strip()

                        # 清理价格格式（移除¥符号等）
                        price_text = price_text.replace("¥", "").replace(",", "")

                        if quantity_text and price_text:
                            quantity = float(quantity_text)
                            price = float(price_text)
                            total = quantity * price

                            # 更新小计列
                            total_item.setText(f"¥{total:.2f}")

                            self.main_window.log_message(f"自动计算小计: {quantity} × {price} = ¥{total:.2f}")

                    except ValueError:
                        # 如果输入的不是有效数字，清空小计
                        total_item.setText("")

        except Exception as e:
            self.main_window.log_message(f"计算小计失败: {str(e)}", "ERROR")

    def update_all_row_numbers(self, table):
        """更新表格中所有行的行号"""
        try:
            for row in range(table.rowCount()):
                row_item = table.item(row, 0)
                if row_item:
                    row_item.setText(str(row + 1))
        except Exception as e:
            self.main_window.log_message(f"更新行号失败: {str(e)}", "ERROR")