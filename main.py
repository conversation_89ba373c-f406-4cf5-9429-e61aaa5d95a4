#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能票据处理系统 - 主启动文件 (PyQt5版本)
现代化界面设计，支持黑色主题和标签页布局
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """静默检查依赖包"""
    required_packages = ['PyQt5', 'requests', 'PIL']

    for package in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'PyQt5':
                import PyQt5
            else:
                __import__(package)
        except ImportError:
            print(f"❌ 缺少依赖包: {package}")
            print("请运行: pip install -r requirements.txt")
            return False

    return True

def main():
    """主函数"""
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    try:
        # 导入并启动PyQt5 GUI
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from modules.pyqt5_main_gui import ModernTicketProcessorGUI

        # 设置高DPI支持
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("智能票据处理系统")
        app.setApplicationVersion("2.0")

        # 创建并显示主窗口
        window = ModernTicketProcessorGUI()
        # 🔥 修复：使用showMaximized()确保全屏显示，不使用show()避免覆盖全屏设置
        window.showMaximized()

        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"导入错误: {e}")
        input("按回车键退出...")
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
