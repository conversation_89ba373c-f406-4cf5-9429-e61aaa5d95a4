#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
颜色规格确认面板 - 固定显示版本（改进状态管理）
集成在主界面右侧的网格卡片布局确认面板
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QScrollArea, QFrame, QGridLayout,
                             QGroupBox, QTextEdit, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal, QSize, QThread, QTimer
from PyQt5.QtGui import QFont, QPalette, QColor, QPixmap
from typing import Dict, List, Any, Optional
import requests
from urllib.parse import urlparse
import os
from modules.return_rate_calculator import ReturnRateCalculator


class ImageLoader(QThread):
    """图像加载线程"""
    image_loaded = pyqtSignal(QPixmap)

    def __init__(self, image_url: str):
        super().__init__()
        self.image_url = image_url

    def run(self):
        """加载图像"""
        try:
            # 多种URL转换策略
            modified_url = self.image_url

            # 策略1：_30x30.jpg -> _150x150.jpg
            if '_30x30.jpg' in self.image_url:
                modified_url = self.image_url.replace('_30x30.jpg', '_150x150.jpg')
            # 策略2：_30x30 -> _150x150 (没有.jpg)
            elif '_30x30' in self.image_url:
                modified_url = self.image_url.replace('_30x30', '_150x150')

            # 设置请求头，模拟浏览器
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://www.taobao.com/',
                'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8'
            }

            # 下载图像
            response = requests.get(modified_url, headers=headers, timeout=10)
            response.raise_for_status()

            # 创建QPixmap
            pixmap = QPixmap()
            success = pixmap.loadFromData(response.content)

            if not pixmap.isNull():
                # 缩放到150x150，保持宽高比
                scaled_pixmap = pixmap.scaled(150, 150, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.image_loaded.emit(scaled_pixmap)

        except Exception as e:
            pass  # 静默处理错误，避免日志污染


class ColorConfirmPanel(QWidget):
    """颜色规格确认面板（固定显示，网格卡片布局）"""
    
    # 信号定义
    color_confirmed = pyqtSignal(int, dict, list)  # 行号, 选择的颜色组字典, 合并的SKU列表

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_row = -1
        self.current_sku_code = ""
        self.current_color_groups = {}
        self.current_ticket_price = 0.0
        self.current_file_name = ""  # 新增：当前文件名

        # 智能选择支持（自动判断单选/多选）
        self.selected_colors = []  # 选中的颜色列表
        self.selected_color_groups = {}  # 选中的颜色组字典 {color_name: skus}
        self.color_cards = []  # 存储颜色卡片

        # 退货率计算器
        self.return_rate_calculator = ReturnRateCalculator()

        self.setup_ui()
        self.setup_dark_theme()
        self.show_empty_state()  # 默认显示空状态
        
    def generate_unique_state_key(self) -> str:
        """生成唯一的状态标识符
        格式：文件名+行号+SKU码
        """
        return f"{self.current_file_name}|row_{self.current_row}|sku_{self.current_sku_code}"

    def get_main_window(self):
        """获取主窗口引用"""
        main_window = self.parent()
        while main_window and not hasattr(main_window, 'color_confirm_states'):
            main_window = main_window.parent()
        return main_window

    def get_current_file_name(self) -> str:
        """获取当前文件名（不含路径）"""
        main_window = self.get_main_window()
        if not main_window:
            return ""

        current_file = None
        
        # 尝试多种方式获取当前文件路径
        if hasattr(main_window, 'current_selected_file') and main_window.current_selected_file:
            current_file = main_window.current_selected_file
        elif hasattr(main_window, 'image_manager') and hasattr(main_window.image_manager, 'current_image_path'):
            current_file = main_window.image_manager.current_image_path
        elif hasattr(main_window, 'file_manager') and hasattr(main_window.file_manager, 'current_file'):
            current_file = main_window.file_manager.current_file
        elif hasattr(main_window, 'table_ui_manager') and hasattr(main_window.table_ui_manager, 'current_file'):
            current_file = main_window.table_ui_manager.current_file

        if current_file:
            return os.path.basename(current_file)
        return ""
        
    def setup_ui(self):
        """设置界面"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(4)  # 减少间距
        main_layout.setContentsMargins(0, 0, 0, 0)  # 去除外边距

        # 创建808px宽度的容器
        self.create_main_container(main_layout)

        # 设置面板大小策略
        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)  # 固定宽度
        self.setFixedWidth(808)  # 固定宽度808px
        self.setMinimumHeight(550)
        self.setMaximumHeight(650)

    def create_main_container(self, layout):
        """创建主容器（808px宽度，无外边框）"""
        # 主容器框架
        container_frame = QFrame()
        container_frame.setFrameStyle(QFrame.NoFrame)  # 去除外边框
        container_frame.setFixedWidth(808)
        container_frame.setStyleSheet("""
            QFrame {
                background-color: #161617;
                border-radius: 8px;
            }
        """)
        layout.addWidget(container_frame)

        # 容器内部布局
        container_layout = QVBoxLayout(container_frame)
        container_layout.setSpacing(0)  # 紧凑布局
        container_layout.setContentsMargins(12, 12, 12, 12)

        # 款号和成本价信息区域
        self.create_product_info_section(container_layout)

        # 添加1px分割线
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("""
            QFrame {
                color: #555555;
                background-color: #555555;
                border: none;
                max-height: 1px;
                min-height: 1px;
            }
        """)
        container_layout.addWidget(separator)

        # 颜色规格网格区域
        self.create_color_grid_section(container_layout)

        # 操作按钮区域
        self.create_action_buttons(container_layout)

    def create_product_info_section(self, layout):
        """创建商品信息区域（款号和成本价）"""
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.NoFrame)
        info_frame.setMaximumHeight(80)
        info_frame.setStyleSheet("""
            QFrame {
                background-color: transparent;
                border: none;
            }
        """)
        layout.addWidget(info_frame)

        info_layout = QVBoxLayout(info_frame)
        info_layout.setContentsMargins(0, 8, 0, 8)
        info_layout.setSpacing(6)

        # 款号行（居中显示，14号字体加粗）
        sku_layout = QHBoxLayout()
        sku_layout.addStretch()

        self.sku_label = QLabel("🪧 款号: --")
        self.sku_label.setFont(QFont("", 14, QFont.Bold))
        self.sku_label.setAlignment(Qt.AlignCenter)
        self.sku_label.setStyleSheet("color: #ffffff;")
        sku_layout.addWidget(self.sku_label)

        sku_layout.addStretch()
        info_layout.addLayout(sku_layout)

        # 成本价行（右对齐，14号字体加粗）
        price_layout = QHBoxLayout()
        price_layout.addStretch()

        self.price_label = QLabel("成本价: ¥--")
        self.price_label.setFont(QFont("", 14, QFont.Bold))
        self.price_label.setStyleSheet("color: #4CAF50;")
        price_layout.addWidget(self.price_label)

        info_layout.addLayout(price_layout)

        # 保留状态标签用于内部状态显示（不显示给用户）
        self.status_label = QLabel("")
        self.status_label.setVisible(False)


        
    def create_color_grid_section(self, layout):
        """创建颜色规格网格区域（4列布局）"""
        # 滚动区域容器
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setMaximumHeight(480)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
        """)
        layout.addWidget(scroll_area)

        # 网格容器
        self.grid_widget = QWidget()
        scroll_area.setWidget(self.grid_widget)

        # 网格布局（调整为4列布局）
        self.grid_layout = QGridLayout(self.grid_widget)
        self.grid_layout.setSpacing(8)  # 减少间距
        self.grid_layout.setContentsMargins(8, 12, 8, 12)

        # 空状态提示
        self.empty_label = QLabel("请点击表格行选择需要确认的商品")
        self.empty_label.setAlignment(Qt.AlignCenter)
        self.empty_label.setStyleSheet("""
            color: #888888;
            font-size: 12px;
            padding: 40px;
            border: 2px dashed #555555;
            border-radius: 8px;
            background-color: #161617;
        """)
        self.grid_layout.addWidget(self.empty_label, 0, 0, 1, 4)  # 跨4列显示

    def create_action_buttons(self, layout):
        """创建操作按钮"""
        # 操作按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        # 清除选择按钮
        self.clear_btn = QPushButton("🔄 清除选择")
        self.clear_btn.clicked.connect(self.clear_selection)
        self.clear_btn.setMinimumHeight(32)
        self.clear_btn.setEnabled(False)
        button_layout.addWidget(self.clear_btn)

        button_layout.addStretch()

        # 确认按钮
        self.confirm_btn = QPushButton("✅ 确认选择")
        self.confirm_btn.clicked.connect(self.confirm_selection)
        self.confirm_btn.setMinimumHeight(32)
        self.confirm_btn.setEnabled(False)
        button_layout.addWidget(self.confirm_btn)

        layout.addLayout(button_layout)
        
    def setup_dark_theme(self):
        """设置深色主题"""
        self.setStyleSheet("""
            QWidget {
                background-color: #1e1e1e;
                color: #ffffff;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            
            QPushButton {
                background-color: #0071e3;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 12px;
            }
            
            QPushButton:hover {
                background-color: #005bb5;
            }
            
            QPushButton:pressed {
                background-color: #004080;
            }
            
            QPushButton:disabled {
                background-color: #3d3d3d;
                color: #888888;
            }
            
            QScrollArea {
                border: 1px solid #555555;
                border-radius: 8px;
                background-color: #161617;
            }
            
            QScrollBar:vertical {
                background-color: #2d2d2d;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background-color: #666666;
            }
        """)
        
    def load_confirmation_data(self, row: int, sku_code: str, color_groups: Dict[str, List[Dict[str, Any]]],
                              ticket_price: float, table_color_spec: str = "", strategy: str = ""):
        """加载确认数据到固定面板（改进的状态管理）"""
        print(f"🔄 加载确认数据: 行{row+1}, SKU={sku_code}")
        
        # 更新当前状态信息
        self.current_row = row
        self.current_sku_code = sku_code
        self.current_color_groups = color_groups
        self.current_ticket_price = ticket_price
        self.current_file_name = self.get_current_file_name()
        self.table_color_spec = table_color_spec
        self.current_strategy = strategy

        # 生成当前的唯一状态标识符
        current_state_key = self.generate_unique_state_key()
        print(f"🔑 当前状态标识符: {current_state_key}")

        # 清除之前的选择状态（每次切换都重新开始）
        self.selected_colors.clear()
        self.selected_color_groups.clear()

        # 更新标题信息
        if table_color_spec:
            self.sku_label.setText(f"🪧 款号: {sku_code} - {table_color_spec}")
        else:
            color_specs = list(color_groups.keys())
            if len(color_specs) == 1:
                self.sku_label.setText(f"🪧 款号: {sku_code} - {color_specs[0]}")
            else:
                self.sku_label.setText(f"🪧 款号: {sku_code} - {len(color_groups)}个颜色规格")

        self.price_label.setText(f"成本价: ¥{ticket_price}")
        # self.status_label.setText(f"共{len(color_groups)}个颜色规格")  # 已隐藏状态显示
        
        # 重建颜色卡片
        self.clear_color_cards()
        self.create_color_cards(color_groups, ticket_price)

        # 尝试恢复保存的状态
        self.restore_saved_state(current_state_key)

        # 根据策略设置按钮状态
        if strategy == "auto_update" and len(color_groups) == 1:
            self.confirm_btn.setVisible(False)
            self.clear_btn.setVisible(False)
            # self.status_label.setText("单颜色规格 - 无需确认")  # 已隐藏状态显示
        else:
            self.confirm_btn.setVisible(True)
            self.clear_btn.setVisible(True)
            self.update_button_states()

    def save_confirmation_state(self):
        """保存确认状态（改进版本）"""
        try:
            if not self.selected_colors or self.current_row < 0:
                return

            main_window = self.get_main_window()
            if not main_window:
                print("❌ 无法获取主窗口")
                return

            # 初始化状态存储
            if not hasattr(main_window, 'color_confirm_states'):
                main_window.color_confirm_states = {}

            # 生成唯一状态标识符
            state_key = self.generate_unique_state_key()
            
            # 准备状态数据
            confirm_data = {
                'selected_colors': self.selected_colors.copy(),
                'selected_color_groups': self.selected_color_groups.copy(),
                'current_sku_code': self.current_sku_code,
                'current_ticket_price': self.current_ticket_price,
                'table_color_spec': getattr(self, 'table_color_spec', ''),
                'is_confirmed': True,
                'state_key': state_key  # 记录状态标识符
            }

            # 保存状态
            main_window.color_confirm_states[state_key] = confirm_data
            
            print(f"✅ 状态保存成功")
            print(f"🔑 状态标识符: {state_key}")
            print(f"🎨 保存的颜色: {self.selected_colors}")
            
        except Exception as e:
            print(f"❌ 保存确认状态失败: {str(e)}")

    def restore_saved_state(self, state_key: str):
        """恢复保存的状态（改进版本）"""
        try:
            main_window = self.get_main_window()
            if not main_window or not hasattr(main_window, 'color_confirm_states'):
                print("🔍 没有找到状态存储")
                return

            if state_key not in main_window.color_confirm_states:
                print(f"🔍 没有找到状态: {state_key}")
                return

            confirm_data = main_window.color_confirm_states[state_key]
            if not confirm_data.get('is_confirmed', False):
                print("⚠️ 数据未确认，跳过恢复")
                return

            # 恢复选择状态
            self.selected_colors = confirm_data.get('selected_colors', []).copy()
            self.selected_color_groups = confirm_data.get('selected_color_groups', {}).copy()
            
            print(f"🔄 恢复状态成功")
            print(f"🎨 恢复的颜色: {self.selected_colors}")

            # 恢复卡片的确认状态
            for card in self.color_cards:
                if card.color_name in self.selected_colors:
                    card.set_confirmed(True)
                    card.update_style()
                else:
                    card.set_confirmed(False)

            # 更新UI状态
            self.confirm_btn.setEnabled(False)
            self.confirm_btn.setText("✅ 已确认")
            self.clear_btn.setEnabled(True)

            # 更新状态显示（状态显示已隐藏）
            confirmed_count = len(self.selected_colors)

        except Exception as e:
            print(f"❌ 恢复确认状态失败: {str(e)}")

    def clear_color_cards(self):
        """清空颜色卡片"""
        for card in self.color_cards:
            card.setParent(None)
            card.deleteLater()
        self.color_cards.clear()
        
        # 清空网格布局
        while self.grid_layout.count():
            child = self.grid_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)

    def create_color_cards(self, color_groups: Dict[str, List[Dict[str, Any]]], ticket_price: float):
        """创建颜色规格卡片（4列布局，按售价从低到高排序）"""
        row = 0
        col = 0
        max_cols = 4  # 每行4个卡片

        # 先识别商品链接，然后为每个颜色分配正确的商品链接SKU数据
        all_skus = []
        for color_name, skus in color_groups.items():
            all_skus.extend(skus)

        # 识别商品链接
        from modules.product_link_identifier import ProductLinkIdentifier
        identifier = ProductLinkIdentifier()
        product_links = identifier.identify_product_links(all_skus)

        # 按售价排序颜色规格组
        sorted_color_groups = self.sort_color_groups_by_price(color_groups)

        for color_name, skus in sorted_color_groups:
            # 从SKU数据中提取完整的款号信息
            full_sku_code = self.extract_full_sku_code(skus)

            # 创建卡片，传递完整的款号用于显示"款号-颜色"格式
            card = ColorSpecCard(color_name, skus, ticket_price, full_sku_code, self)

            # 为每个颜色卡片设置它所属商品链接的SKU数据
            color_link_skus = self._find_color_product_link_skus(skus, product_links)
            card.set_all_skus(color_link_skus)
            card.card_clicked.connect(self.on_card_clicked)

            # 添加到网格布局
            self.grid_layout.addWidget(card, row, col)
            self.color_cards.append(card)

            # 计算下一个位置
            col += 1
            if col >= max_cols:
                col = 0
                row += 1

    def extract_full_sku_code(self, skus: List[Dict[str, Any]]) -> str:
        """从SKU数据中提取完整的款号信息

        Args:
            skus: SKU列表

        Returns:
            完整的款号，如"GD340-1011"
        """
        if not skus:
            return self.current_sku_code

        # 从第一个SKU中获取sku_id
        first_sku = skus[0]
        sku_id = first_sku.get('sku_id', '')

        if not sku_id:
            return self.current_sku_code

        # sku_id格式通常是: "GD340-1011-黑色-S"
        # 我们需要提取前两个部分: "GD340-1011"
        parts = sku_id.split('-')
        if len(parts) >= 2:
            # 取前两个部分作为完整款号
            full_sku_code = f"{parts[0]}-{parts[1]}"
            print(f"🔍 从SKU ID提取完整款号: {sku_id} -> {full_sku_code}")
            return full_sku_code
        else:
            # 如果格式不符合预期，返回原始的sku_code
            return self.current_sku_code

    def sort_color_groups_by_price(self, color_groups: Dict[str, List[Dict[str, Any]]]) -> List[tuple]:
        """按价格排序颜色组（从低到高）"""
        def get_max_price(skus):
            try:
                return max((sku.get('sale_price', 0) for sku in skus), default=0)
            except:
                return 0

        return sorted(color_groups.items(), key=lambda x: get_max_price(x[1]), reverse=False)

    def show_empty_state(self):
        """显示空状态"""
        self.clear_color_cards()
        
        # 重新添加空状态标签
        self.empty_label = QLabel("请点击表格行选择需要确认的商品")
        self.empty_label.setAlignment(Qt.AlignCenter)
        self.empty_label.setStyleSheet("""
            color: #888888;
            font-size: 12px;
            padding: 40px;
            border: 2px dashed #555555;
            border-radius: 8px;
            background-color: #161617;
        """)
        self.grid_layout.addWidget(self.empty_label, 0, 0)
        
        # 重置状态
        self.current_row = -1
        self.current_sku_code = ""
        self.current_color_groups = {}
        self.current_ticket_price = 0.0
        self.current_file_name = ""
        self.selected_colors.clear()
        self.selected_color_groups.clear()

    def on_card_clicked(self, color_name: str, skus: List[Dict[str, Any]]):
        """处理卡片点击事件"""
        # 如果卡片已确认，不允许修改
        clicked_card = None
        for card in self.color_cards:
            if card.color_name == color_name:
                clicked_card = card
                break
                
        if clicked_card and clicked_card.is_confirmed:
            print(f"🔒 卡片已确认，不允许修改: {color_name}")
            return

        # 切换选择状态
        if color_name in self.selected_colors:
            self.selected_colors.remove(color_name)
            self.selected_color_groups.pop(color_name, None)
        else:
            self.selected_colors.append(color_name)
            self.selected_color_groups[color_name] = skus

        self.update_card_selection_display()
        self.update_status_display()
        self.update_button_states()

    def update_card_selection_display(self):
        """更新卡片选择显示"""
        for card in self.color_cards:
            if not card.is_confirmed:  # 只更新未确认的卡片
                card.set_selected(card.color_name in self.selected_colors)

    def update_status_display(self):
        """更新状态显示（已隐藏状态标签）"""
        # 状态显示已隐藏，保留方法以维持兼容性
        pass

    def update_button_states(self):
        """更新按钮状态"""
        has_selection = bool(self.selected_colors)
        self.confirm_btn.setEnabled(has_selection)
        self.clear_btn.setEnabled(has_selection)

    def clear_selection(self):
        """清除选择（改进版本）"""
        # 清除确认状态的卡片
        for card in self.color_cards:
            if card.is_confirmed:
                card.set_confirmed(False)
                card.update_style()

        # 清除选择状态
        self.selected_colors.clear()
        self.selected_color_groups.clear()

        # 删除保存的状态
        state_key = self.generate_unique_state_key()
        main_window = self.get_main_window()
        if main_window and hasattr(main_window, 'color_confirm_states'):
            main_window.color_confirm_states.pop(state_key, None)
            print(f"🗑️ 删除保存的状态: {state_key}")

        # 更新显示
        self.update_card_selection_display()
        self.update_status_display()
        self.update_button_states()
        
        # 重置按钮
        self.confirm_btn.setText("✅ 确认选择")

    def confirm_selection(self):
        """确认选择"""
        if self.selected_colors and self.selected_color_groups and self.current_row >= 0:
            # 发送整个商品链接的SKU数据，而不是只发送选中颜色规格的SKU
            # 这样表格的退货率和30天实发才能按商品链接级别计算

            # 获取选中颜色所属的商品链接的所有SKU
            all_link_skus = []
            selected_link_ids = set()

            # 首先识别选中颜色所属的商品链接
            from modules.product_link_identifier import ProductLinkIdentifier
            identifier = ProductLinkIdentifier()

            # 收集所有颜色的SKU用于商品链接识别
            all_color_skus = []
            for color_groups in self.current_color_groups.values():
                all_color_skus.extend(color_groups)

            product_links = identifier.identify_product_links(all_color_skus)
            print(f"🔗 识别出 {len(product_links)} 个商品链接")

            # 找到选中颜色所属的商品链接
            for color_name in self.selected_colors:
                if color_name in self.selected_color_groups:
                    color_skus = self.selected_color_groups[color_name]
                    if color_skus:
                        representative_sku_id = color_skus[0].get('sku_id', '')

                        # 找到这个颜色所属的商品链接
                        for link_id, link_skus in product_links.items():
                            for link_sku in link_skus:
                                if link_sku.get('sku_id', '') == representative_sku_id:
                                    selected_link_ids.add(link_id)
                                    break

            # 收集所有选中商品链接的SKU
            for link_id in selected_link_ids:
                if link_id in product_links:
                    all_link_skus.extend(product_links[link_id])

            # 发送确认信号（发送整个商品链接的SKU数据）
            self.color_confirmed.emit(self.current_row, self.selected_color_groups.copy(), all_link_skus)

            # 设置卡片为确认状态
            for card in self.color_cards:
                if card.color_name in self.selected_colors:
                    card.set_confirmed(True)
                else:
                    card.set_confirmed(False)

            # 保存确认状态
            self.save_confirmation_state()

            # 更新UI状态（状态显示已隐藏）
            confirmed_count = len(self.selected_colors)

            # 更新按钮状态
            self.confirm_btn.setEnabled(False)
            self.confirm_btn.setText("✅ 已确认")
            self.clear_btn.setEnabled(True)

    def _find_color_product_link_skus(self, color_skus: List[Dict[str, Any]], product_links: Dict[str, List[Dict]]) -> List[Dict[str, Any]]:
        """
        找到某个颜色的SKU所属的商品链接，并返回该商品链接的所有SKU

        Args:
            color_skus: 某个颜色的SKU列表
            product_links: 商品链接字典 {"link_1": [sku1, sku2], "link_2": [sku3, sku4]}

        Returns:
            该颜色所属商品链接的所有SKU列表
        """
        if not color_skus or not product_links:
            return color_skus

        # 取第一个SKU作为代表来确定商品链接
        representative_sku = color_skus[0]
        representative_id = representative_sku.get('sku_id', '')

        print(f"🔍 查找颜色SKU所属商品链接: {representative_id}")

        # 在所有商品链接中查找包含这个SKU的链接
        for link_id, link_skus in product_links.items():
            for link_sku in link_skus:
                if link_sku.get('sku_id', '') == representative_id:
                    print(f"🔗 找到匹配的商品链接: {link_id}, 包含 {len(link_skus)} 个SKU")
                    return link_skus

        # 如果没找到匹配的商品链接，返回原始的颜色SKU
        print(f"⚠️ 未找到匹配的商品链接，使用原始颜色SKU")
        return color_skus

    def hide_panel(self):
        """隐藏面板（兼容性方法，实际不隐藏）"""
        self.show_empty_state()


class ColorSpecCard(QFrame):
    """颜色规格卡片"""

    # 信号定义
    card_clicked = pyqtSignal(str, list)  # 颜色名称, SKU列表

    def __init__(self, color_name: str, skus: List[Dict[str, Any]], ticket_price: float, sku_code: str = "", parent=None):
        super().__init__(parent)
        self.color_name = color_name
        self.skus = skus  # 当前颜色规格的SKU列表
        self.ticket_price = ticket_price
        self.sku_code = sku_code
        self.is_selected = False
        self.is_confirmed = False  # 是否已确认状态
        self.all_skus = []  # 🔥 新增：整个商品链接的所有SKU列表（用于退货率计算）

        self.setup_ui()
        self.setup_style()

    def setup_ui(self):
        """设置卡片界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(6, 6, 6, 6)
        layout.setSpacing(4)

        # 图像区域（150x150px，居中显示，适应180px卡片宽度）
        image_container = QHBoxLayout()
        image_container.addStretch()

        self.image_label = QLabel()
        self.image_label.setFixedSize(150, 150)
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet("""
            QLabel {
                background-color: #161617;
                border: 2px dashed #666666;
                border-radius: 8px;
                color: #888888;
                font-size: 12px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
        """)
        self.image_label.setText("加载中...")

        image_container.addWidget(self.image_label)
        image_container.addStretch()
        layout.addLayout(image_container)

        # 加载图像
        self.load_image()

        # 颜色名称（主标题）- 显示为"款号-颜色"格式
        if self.sku_code:
            display_text = f"{self.sku_code}-{self.color_name}"
        else:
            display_text = self.color_name

        color_label = QLabel(display_text)
        color_label.setFont(QFont("", 11, QFont.Bold))
        color_label.setWordWrap(True)
        color_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(color_label)

        # 价格信息
        if self.skus:
            sale_price = self.skus[0].get('sale_price', 0)
            erp_cost_price = self.skus[0].get('cost_price', None)

            # 售价（减去100后显示）
            display_price = sale_price - 100 if sale_price > 100 else sale_price
            sale_label = QLabel(f"售价: ¥{display_price}")
            sale_label.setFont(QFont("", 11))
            sale_label.setAlignment(Qt.AlignCenter)
            sale_label.setStyleSheet("color: #FFC107;")
            layout.addWidget(sale_label)

            # 成本价
            if erp_cost_price is not None and erp_cost_price != "" and erp_cost_price != 0:
                cost_display = f"¥{erp_cost_price}"
            else:
                cost_display = "-"

            cost_label = QLabel(f"成本: {cost_display}")
            cost_label.setFont(QFont("", 11))
            cost_label.setAlignment(Qt.AlignCenter)
            cost_label.setStyleSheet("color: #4CAF50;")
            layout.addWidget(cost_label)

            # 退货率信息
            return_rate_text = self.get_return_rate_display()
            if return_rate_text != "-":
                return_rate_label = QLabel(f"退货率: {return_rate_text}")
                return_rate_label.setFont(QFont("", 10))
                return_rate_label.setAlignment(Qt.AlignCenter)
                return_rate_label.setStyleSheet("color: #FF9800;")  # 橙色
                layout.addWidget(return_rate_label)

            # 利润和毛利率计算
            if display_price > 0:
                if erp_cost_price is not None and erp_cost_price != "" and erp_cost_price != 0 and erp_cost_price != "-":
                    try:
                        erp_cost_float = float(erp_cost_price)
                        profit = display_price - erp_cost_float
                        margin_rate = (profit / display_price) * 100

                        # 利润栏
                        profit_color = "#FF5722" if profit < 20 else "#4CAF50"
                        profit_label = QLabel(f"利润: ¥{profit:.1f}")
                        profit_label.setFont(QFont("", 11))
                        profit_label.setAlignment(Qt.AlignCenter)
                        profit_label.setStyleSheet(f"color: {profit_color};")
                        layout.addWidget(profit_label)

                        # 毛利栏
                        margin_color = "#4CAF50" if margin_rate >= 20 else "#FF5722"
                        margin_label = QLabel(f"毛利: {margin_rate:.1f}%")
                        margin_label.setFont(QFont("", 11))
                        margin_label.setAlignment(Qt.AlignCenter)
                        margin_label.setStyleSheet(f"color: {margin_color};")
                        layout.addWidget(margin_label)

                    except (ValueError, TypeError):
                        # ERP成本价转换失败
                        profit_label = QLabel("💵 利润: --")
                        profit_label.setFont(QFont("", 11))
                        profit_label.setAlignment(Qt.AlignCenter)
                        profit_label.setStyleSheet("color: #888888;")
                        layout.addWidget(profit_label)

                        margin_label = QLabel("📊 毛利: --")
                        margin_label.setFont(QFont("", 11))
                        margin_label.setAlignment(Qt.AlignCenter)
                        margin_label.setStyleSheet("color: #888888;")
                        layout.addWidget(margin_label)
                else:
                    # 使用票据价格预估
                    try:
                        estimated_profit = display_price - self.ticket_price
                        estimated_margin = (estimated_profit / display_price) * 100

                        profit_color = "#FF5722" if estimated_profit < 10 else "#4CAF50"
                        profit_label = QLabel(f"💵 利润: ¥{estimated_profit:.1f}*")
                        profit_label.setFont(QFont("", 11))
                        profit_label.setAlignment(Qt.AlignCenter)
                        profit_label.setStyleSheet(f"color: {profit_color};")
                        layout.addWidget(profit_label)

                        margin_color = "#4CAF50" if estimated_margin >= 20 else "#FF5722"
                        margin_label = QLabel(f"📊 毛利: {estimated_margin:.1f}%*")
                        margin_label.setFont(QFont("", 11))
                        margin_label.setAlignment(Qt.AlignCenter)
                        margin_label.setStyleSheet(f"color: {margin_color};")
                        layout.addWidget(margin_label)

                        # 删除说明文本以简化界面
                    except (ValueError, TypeError):
                        pass

    def load_image(self):
        """加载图像"""
        try:
            # 从SKU数据中获取图像URL
            if self.skus and len(self.skus) > 0:
                # 取第一个SKU的图像URL
                first_sku = self.skus[0]

                if isinstance(first_sku, dict):
                    # 尝试多个可能的图像字段，优先使用大图
                    image_url = first_sku.get('pic_big', '') or first_sku.get('pic', '') or first_sku.get('image', '') or first_sku.get('pic_url', '') or first_sku.get('img', '')

                    if image_url and image_url.strip() and image_url != 'null' and image_url != 'None':
                        # 启动图像加载线程
                        self.image_loader = ImageLoader(image_url.strip())
                        self.image_loader.image_loaded.connect(self.on_image_loaded)
                        self.image_loader.start()
                        return  # 成功启动加载，直接返回

                # 如果到这里，说明没有有效的图像URL
                self.show_placeholder()
            else:
                # 没有SKU数据，显示占位符
                self.show_placeholder()

        except Exception as e:
            self.show_placeholder()

    def on_image_loaded(self, pixmap: QPixmap):
        """图像加载完成回调"""
        try:
            self.image_label.setPixmap(pixmap)
            self.image_label.setText("")  # 清除文字
            # 移除虚线边框，只保留圆角
            self.image_label.setStyleSheet("""
                QLabel {
                    background-color: #161617;
                    border: 1px solid #555555;
                    border-radius: 8px;
                }
            """)
        except Exception as e:
            self.show_placeholder()

    def show_placeholder(self):
        """显示占位符"""
        self.image_label.setText("暂无图片")
        self.image_label.setStyleSheet("""
            QLabel {
                background-color: #161617;
                border: 2px dashed #666666;
                border-radius: 8px;
                color: #888888;
                font-size: 12px;
            }
        """)

    def set_all_skus(self, all_skus: List[Dict[str, Any]]):
        """设置整个商品链接的所有SKU数据（用于退货率计算）"""
        self.all_skus = all_skus

        # 设置数据后重新计算退货率并更新UI
        self._refresh_return_rate_display()

    def _refresh_return_rate_display(self):
        """重新计算并更新退货率显示"""
        try:
            # 重新计算退货率
            new_rate = self.get_return_rate_display()

            # 查找并更新退货率标签
            for child in self.findChildren(QLabel):
                if child.text().startswith("退货率:"):
                    new_text = f"退货率: {new_rate}"
                    child.setText(new_text)
                    break

            # 强制重绘界面
            self.update()
            self.repaint()

        except Exception as e:
            print(f"❌ 刷新退货率显示失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def get_return_rate_display(self) -> str:
        """获取退货率显示文本（按商品链接级别计算30天退货率）"""
        try:
            # 使用整个商品链接的SKU数据计算退货率，而不是单个颜色规格
            skus_for_calculation = self.all_skus if self.all_skus else self.skus

            if not skus_for_calculation:
                return "-"

            # 使用增强的退货率计算器
            from modules.return_rate_calculator import ReturnRateCalculator
            calculator = ReturnRateCalculator()
            result = calculator.get_card_return_rate_30_day(skus_for_calculation)
            return result

        except Exception as e:
            print(f"❌ 获取退货率显示失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return "-"

    def setup_style(self):
        """设置卡片样式"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setCursor(Qt.PointingHandCursor)
        # 设置卡片尺寸（180px宽度，320px高度，适合4列布局，更好展示图像）
        self.setFixedSize(180, 320)
        self.update_style()

    def update_style(self):
        """更新样式"""
        if self.is_confirmed:
            # 已确认状态：绿色背景，最高优先级
            self.setStyleSheet("""
                QFrame {
                    background-color: #1b5e20;
                    border: 2px solid #4caf50;
                    border-radius: 8px;
                    color: #ffffff;
                }
                QLabel {
                    color: #ffffff;
                }
            """)
        elif self.is_selected:
            # 选中状态：蓝色背景
            self.setStyleSheet("""
                QFrame {
                    background-color: #1a365d;
                    border: 2px solid #0078d4;
                    border-radius: 8px;
                    color: #ffffff;
                }
                QFrame:hover {
                    background-color: #2a5a88;
                    border: 2px solid #106ebe;
                }
                QLabel {
                    color: #ffffff;
                }
            """)
        else:
            # 未选择状态：灰色背景
            self.setStyleSheet("""
                QFrame {
                    background-color: #161617;
                    border: 1px solid #555555;
                    border-radius: 8px;
                    color: #ffffff;
                }
                QFrame:hover {
                    background-color: #1a1a1b;
                    border: 1px solid #0071e3;
                }
                QLabel {
                    color: #ffffff;
                }
            """)

    def mousePressEvent(self, event):
        """鼠标点击事件"""
        if event.button() == Qt.LeftButton:
            self.card_clicked.emit(self.color_name, self.skus)
        super().mousePressEvent(event)

    def set_selected(self, selected: bool):
        """设置选中状态（保护已确认状态）"""
        if not self.is_confirmed:
            self.is_selected = selected
            self.update_style()

    def set_confirmed(self, confirmed: bool):
        """设置确认状态"""
        self.is_confirmed = confirmed
        if confirmed:
            self.is_selected = False
        self.update_style()
