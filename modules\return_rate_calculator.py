#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
退货率计算模块
按商品链接聚合SKU数据并计算退货率（只计算15天和30天）
"""

from typing import Dict, List, Any, Optional
from collections import defaultdict
from .product_link_identifier import ProductLinkIdentifier


class ReturnRateCalculator:
    """退货率计算器（增强版，支持商品链接级别计算）"""

    def __init__(self):
        self.link_identifier = ProductLinkIdentifier()
    
    def calculate_return_rates_by_sku_code(self, skus: List[Dict[str, Any]]) -> Dict[str, float]:
        """
        按款号计算退货率（兼容旧接口，只计算15天和30天）

        Args:
            skus: SKU列表，每个SKU包含退货率相关字段

        Returns:
            退货率字典: {"15天": 75.5, "30天": 68.2}
        """
        if not skus:
            return {}

        # 按款号聚合数据
        aggregated_data = self._aggregate_skus_by_code(skus)

        # 计算退货率（只计算15天和30天）
        return_rates = {}
        for period in ["15天", "30天"]:  # 🔥 删除7天
            period_num = period.replace("天", "")
            sent_key = f"sent_qty_{period_num}"
            as_key = f"as_qty_{period_num}"

            total_sent = aggregated_data.get(sent_key, 0)
            total_returned = aggregated_data.get(as_key, 0)

            if total_sent > 0:
                return_rate = (total_returned / total_sent) * 100
                return_rates[period] = round(return_rate, 2)
            else:
                return_rates[period] = -1  # 表示无数据

        return return_rates

    def calculate_return_rates_by_product_links(self, skus: List[Dict[str, Any]]) -> Dict[str, Dict[str, float]]:
        """
        按商品链接计算退货率（只计算15天和30天）

        Args:
            skus: SKU列表，每个SKU包含退货率相关字段

        Returns:
            按商品链接分组的退货率: {
                "link_1": {"15天": 15.5, "30天": 12.3},
                "link_2": {"15天": 25.8, "30天": 18.9}
            }
        """
        if not skus:
            print("🔍 ReturnRateCalculator: SKU列表为空，返回空结果")
            return {}

        print(f"🔍 ReturnRateCalculator: 开始计算 {len(skus)} 个SKU的商品链接退货率")

        # 🔍 调试：检查SKU数据结构
        if skus:
            first_sku = skus[0]
            print(f"🔍 第一个SKU数据: {first_sku.get('sku_id', 'None')}")
            print(f"🔍 商品名称字段: name={first_sku.get('name', 'None')}, item_name={first_sku.get('item_name', 'None')}")
            print(f"🔍 退货率字段: sent_qty_15={first_sku.get('sent_qty_15', 'None')}, as_qty_15={first_sku.get('as_qty_15', 'None')}")

        # 识别商品链接
        product_links = self.link_identifier.identify_product_links(skus)
        print(f"🔗 识别出 {len(product_links)} 个商品链接")

        # 为每个商品链接计算退货率
        link_return_rates = {}
        for link_id, link_skus in product_links.items():
            print(f"🔍 计算 {link_id} 的退货率，包含 {len(link_skus)} 个SKU")
            link_return_rates[link_id] = self._calculate_single_link_rate(link_skus)
            print(f"   结果: {link_return_rates[link_id]}")

        return link_return_rates

    def _calculate_single_link_rate(self, skus: List[Dict[str, Any]]) -> Dict[str, float]:
        """计算单个商品链接的退货率（只计算15天和30天）"""
        aggregated_data = self._aggregate_skus_by_code(skus)

        return_rates = {}
        for period in ["15天", "30天"]:  # 🔥 删除7天
            period_num = period.replace("天", "")
            sent_key = f"sent_qty_{period_num}"
            as_key = f"as_qty_{period_num}"

            total_sent = aggregated_data.get(sent_key, 0)
            total_returned = aggregated_data.get(as_key, 0)

            if total_sent > 0:
                return_rate = (total_returned / total_sent) * 100
                return_rates[period] = round(return_rate, 2)
            else:
                return_rates[period] = -1

        return return_rates
    
    def _aggregate_skus_by_code(self, skus: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        按款号聚合SKU数据（只聚合15天和30天数据）

        Args:
            skus: 同一款号下的所有SKU列表

        Returns:
            聚合后的数据: {"sent_qty_15": 41, "as_qty_15": 34, "sent_qty_30": 50, "as_qty_30": 40}
        """
        aggregated = {
            "sent_qty_15": 0,
            "as_qty_15": 0,
            "sent_qty_30": 0,
            "as_qty_30": 0,
        }

        for sku in skus:
            for key in aggregated.keys():
                value = sku.get(key, 0)
                if isinstance(value, (int, float)) and value > 0:
                    aggregated[key] += int(value)

        return aggregated

    def calculate_sent_qty_30_by_links(self, items: List[Dict[str, Any]]) -> Dict[str, int]:
        """
        计算每个商品链接的30天实发数量总和

        Args:
            items: 商品数据列表

        Returns:
            Dict[str, int]: {商品链接: 30天实发总数}
        """
        link_sent_qty = {}

        # 按商品链接分组
        for item in items:
            product_link = item.get('product_link', '')
            if not product_link:
                continue

            sent_qty_30 = item.get('sent_qty_30', 0)
            if isinstance(sent_qty_30, (int, float)) and sent_qty_30 > 0:
                if product_link not in link_sent_qty:
                    link_sent_qty[product_link] = 0
                link_sent_qty[product_link] += int(sent_qty_30)

        return link_sent_qty

    def format_return_rate_display(self, return_rates: Dict[str, float]) -> str:
        """
        格式化退货率显示文本（只显示15天和30天）

        Args:
            return_rates: 退货率字典

        Returns:
            格式化的显示文本，如 "15天:75.5% | 30天:68.2%"
        """
        if not return_rates:
            return "-"

        parts = []
        for period in ["15天", "30天"]:  # 🔥 删除7天
            if period in return_rates:
                rate = return_rates[period]
                if rate >= 0:  # 只显示有效的退货率
                    parts.append(f"{period}:{rate:.1f}%")

        return " | ".join(parts) if parts else "-"

    def format_return_rate_integer(self, return_rates: Dict[str, float]) -> Dict[str, str]:
        """
        格式化退货率为整数显示（用于表格两列显示：15退、30退）

        Args:
            return_rates: 退货率字典

        Returns:
            格式化的退货率字典，如 {"15天": "76%", "30天": "68%"}
        """
        if not return_rates:
            return {"15天": "-", "30天": "-"}

        result = {}
        for period in ["15天", "30天"]:  # 🔥 删除7天
            if period in return_rates:
                rate = return_rates[period]
                if rate >= 0:  # 只显示有效的退货率
                    result[period] = f"{int(round(rate))}%"
                else:
                    result[period] = "-"
            else:
                result[period] = "-"

        return result

    def format_multi_link_display(self, link_rates: Dict[str, Dict[str, float]]) -> Dict[str, str]:
        """
        格式化多链接显示（用 | 分隔）

        Args:
            link_rates: 按商品链接分组的退货率

        Returns:
            格式化的显示文本: {"15天": "16% | 26%", "30天": "12% | 19%"}
        """
        if not link_rates:
            return {"15天": "-", "30天": "-"}

        if len(link_rates) == 1:
            # 单链接情况
            rates = list(link_rates.values())[0]
            return {
                "15天": f"{int(round(rates['15天']))}%" if rates.get('15天', -1) >= 0 else "-",
                "30天": f"{int(round(rates['30天']))}%" if rates.get('30天', -1) >= 0 else "-"
            }
        else:
            # 多链接情况，用 | 分隔
            result = {}
            for period in ["15天", "30天"]:
                rates_list = []
                for link_rates_data in link_rates.values():
                    rate = link_rates_data.get(period, -1)
                    if rate >= 0:
                        rates_list.append(f"{int(round(rate))}%")
                    else:
                        rates_list.append("-")
                result[period] = " | ".join(rates_list)
            return result
    
    def get_return_rate_color(self, return_rates: Dict[str, float]) -> str:
        """
        根据退货率获取显示颜色（基于30天退货率）

        Args:
            return_rates: 退货率字典

        Returns:
            颜色代码字符串
        """
        if not return_rates:
            return "#FFFFFF"  # 白色

        # 取30天退货率作为主要判断依据
        main_rate = return_rates.get("30天", -1)
        if main_rate < 0:
            return "#FFFFFF"  # 白色 - 无数据
        elif main_rate < 20:
            return "#4CAF50"  # 绿色 - 低退货率
        elif main_rate < 50:
            return "#FF9800"  # 橙色 - 中等退货率
        else:
            return "#ff3e66"  # 统一红色 - 高退货率
    
    def get_return_rate_summary(self, return_rates: Dict[str, float]) -> Dict[str, Any]:
        """
        获取退货率摘要信息
        
        Args:
            return_rates: 退货率字典
            
        Returns:
            摘要信息: {
                "display_text": "7天:82.9% | 15天:75.5%",
                "color": "#F44336",
                "level": "high",  # low, medium, high
                "main_rate": 82.9
            }
        """
        display_text = self.format_return_rate_display(return_rates)
        color = self.get_return_rate_color(return_rates)
        
        main_rate = return_rates.get("30天", -1) if return_rates else -1
        
        if main_rate < 0:
            level = "unknown"
        elif main_rate < 20:
            level = "low"
        elif main_rate < 50:
            level = "medium"
        else:
            level = "high"
        
        return {
            "display_text": display_text,
            "color": color,
            "level": level,
            "main_rate": main_rate
        }

    def get_card_return_rate_30_day(self, skus: List[Dict[str, Any]]) -> str:
        """
        获取商品卡显示的30天退货率

        Args:
            skus: SKU列表

        Returns:
            30天退货率字符串，如 "12%"
        """
        print(f"🔥🔥🔥 get_card_return_rate_30_day() 被调用！SKU数量: {len(skus)}")

        if not skus:
            print(f"🔍 SKU列表为空，返回'-'")
            return "-"

        link_rates = self.calculate_return_rates_by_product_links(skus)
        print(f"🔍 商品链接退货率计算结果: {link_rates}")

        if len(link_rates) == 1:
            # 单链接，直接显示30天退货率
            rates = list(link_rates.values())[0]
            rate_30 = rates.get("30天", -1)
            result = f"{int(round(rate_30))}%" if rate_30 >= 0 else "-"
            print(f"🔍 单链接退货率: {result}")
            return result
        else:
            # 多链接，显示主要链接的30天退货率（SKU数量最多的链接）
            print(f"🔍 多链接情况，选择主要链接")
            main_link_rates = self._select_main_link_by_sku_count(link_rates, skus)
            rate_30 = main_link_rates.get("30天", -1)
            result = f"{int(round(rate_30))}%" if rate_30 >= 0 else "-"
            print(f"🔍 主要链接退货率: {result}")
            return result

    def _select_main_link_by_sku_count(self, link_rates: Dict[str, Dict], skus: List[Dict]) -> Dict[str, float]:
        """
        根据SKU数量选择主要商品链接

        Args:
            link_rates: 商品链接退货率数据
            skus: 原始SKU列表

        Returns:
            主要链接的退货率数据
        """
        if not link_rates:
            return {"15天": -1, "30天": -1}

        # 重新识别商品链接以获取SKU分组
        product_links = self.link_identifier.identify_product_links(skus)

        # 找到SKU数量最多的链接
        max_sku_count = 0
        main_link_id = None

        for link_id, link_skus in product_links.items():
            if len(link_skus) > max_sku_count:
                max_sku_count = len(link_skus)
                main_link_id = link_id

        if main_link_id and main_link_id in link_rates:
            return link_rates[main_link_id]
        else:
            # 如果找不到，返回第一个链接的数据
            return list(link_rates.values())[0] if link_rates else {"15天": -1, "30天": -1}


def test_return_rate_calculator():
    """测试退货率计算器"""
    calculator = ReturnRateCalculator()
    
    # 测试数据：GT217-7528款号下的12个SKU
    test_skus = [
        {"sku_id": "GT217-7528-红色-S", "sent_qty_7": 5, "as_qty_7": 4, "sent_qty_15": 8, "as_qty_15": 6},
        {"sku_id": "GT217-7528-红色-M", "sent_qty_7": 6, "as_qty_7": 5, "sent_qty_15": 10, "as_qty_15": 7},
        {"sku_id": "GT217-7528-红色-L", "sent_qty_7": 7, "as_qty_7": 6, "sent_qty_15": 12, "as_qty_15": 9},
        {"sku_id": "GT217-7528-红色-XL", "sent_qty_7": 3, "as_qty_7": 2, "sent_qty_15": 5, "as_qty_15": 3},
        {"sku_id": "GT217-7528-蓝色-S", "sent_qty_7": 4, "as_qty_7": 3, "sent_qty_15": 7, "as_qty_15": 5},
        {"sku_id": "GT217-7528-蓝色-M", "sent_qty_7": 5, "as_qty_7": 4, "sent_qty_15": 9, "as_qty_15": 6},
        {"sku_id": "GT217-7528-蓝色-L", "sent_qty_7": 6, "as_qty_7": 5, "sent_qty_15": 11, "as_qty_15": 8},
        {"sku_id": "GT217-7528-蓝色-XL", "sent_qty_7": 2, "as_qty_7": 2, "sent_qty_15": 4, "as_qty_15": 2},
        {"sku_id": "GT217-7528-黑色-S", "sent_qty_7": 3, "as_qty_7": 3, "sent_qty_15": 6, "as_qty_15": 4},
        {"sku_id": "GT217-7528-黑色-M", "sent_qty_7": 4, "as_qty_7": 3, "sent_qty_15": 8, "as_qty_15": 5},
        {"sku_id": "GT217-7528-黑色-L", "sent_qty_7": 5, "as_qty_7": 4, "sent_qty_15": 10, "as_qty_15": 7},
        {"sku_id": "GT217-7528-黑色-XL", "sent_qty_7": 1, "as_qty_7": 1, "sent_qty_15": 3, "as_qty_15": 2},
    ]
    
    # 计算退货率
    return_rates = calculator.calculate_return_rates_by_sku_code(test_skus)
    print(f"计算结果: {return_rates}")
    
    # 获取摘要信息
    summary = calculator.get_return_rate_summary(return_rates)
    print(f"摘要信息: {summary}")
    
    # 验证计算结果
    # 7天：实发41件，实退34件 → 退货率 = 34÷41×100% = 82.93%
    total_sent_7 = sum(sku.get("sent_qty_7", 0) for sku in test_skus)
    total_returned_7 = sum(sku.get("as_qty_7", 0) for sku in test_skus)
    expected_rate_7 = (total_returned_7 / total_sent_7) * 100
    
    print(f"验证 - 7天实发: {total_sent_7}, 实退: {total_returned_7}")
    print(f"验证 - 预期退货率: {expected_rate_7:.2f}%")
    print(f"验证 - 计算退货率: {return_rates.get('7天', 0):.2f}%")


if __name__ == "__main__":
    test_return_rate_calculator()
