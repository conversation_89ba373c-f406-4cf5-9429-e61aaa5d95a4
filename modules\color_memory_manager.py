#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
颜色确认智能记忆管理器
负责记录、匹配和管理用户的颜色选择记忆
"""

import json
import os
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime


class ColorMemoryManager:
    """颜色确认智能记忆管理器"""
    
    def __init__(self, memory_file: str = "color_memory.json"):
        self.memory_file = memory_file
        self.memories: Dict[str, Dict] = {}
        self.pending_memories: Dict[str, Dict] = {}  # 暂存的记忆，等待上传时保存
        self.load_memories()
    
    def load_memories(self) -> bool:
        """加载记忆数据"""
        try:
            if os.path.exists(self.memory_file):
                with open(self.memory_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.memories = data.get("memories", {})
                return True
            else:
                self.memories = {}
                return True
        except Exception as e:
            print(f"加载颜色记忆失败: {str(e)}")
            self.memories = {}
            return False
    
    def save_memories(self) -> bool:
        """保存记忆数据到文件"""
        try:
            data = {
                "memories": self.memories,
                "last_updated": datetime.now().isoformat(),
                "version": "1.0"
            }
            
            with open(self.memory_file, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存颜色记忆失败: {str(e)}")
            return False
    
    def generate_memory_key(self, sku_code: str, color_spec: str) -> str:
        """生成记忆键值（款号+颜色规格）"""
        # 清理和标准化键值
        clean_sku = str(sku_code).strip()
        clean_color = str(color_spec).strip()
        return f"{clean_sku}|{clean_color}"
    
    def add_pending_memory(self, sku_code: str, color_spec: str, selected_colors: List[str], 
                          supplier: str = "", file_name: str = "") -> str:
        """添加待保存的记忆（暂存在内存中）"""
        try:
            memory_key = self.generate_memory_key(sku_code, color_spec)
            
            memory_data = {
                "sku_code": sku_code,
                "color_spec": color_spec,
                "selected_colors": selected_colors.copy(),
                "supplier": supplier,
                "file_name": file_name,
                "created_time": datetime.now().isoformat(),
                "use_count": 0,
                "last_used": None,
                "status": "pending"  # 标记为待保存状态
            }
            
            self.pending_memories[memory_key] = memory_data
            print(f"✅ 添加待保存记忆: {memory_key}")
            print(f"🎨 选择的颜色: {selected_colors}")
            
            return memory_key
        except Exception as e:
            print(f"❌ 添加待保存记忆失败: {str(e)}")
            return ""
    
    def commit_pending_memories(self) -> bool:
        """提交所有待保存的记忆到永久存储"""
        try:
            if not self.pending_memories:
                return True
            
            committed_count = 0
            for memory_key, memory_data in self.pending_memories.items():
                # 检查是否已存在相同记忆
                if memory_key in self.memories:
                    # 更新现有记忆
                    existing = self.memories[memory_key]
                    existing["selected_colors"] = memory_data["selected_colors"]
                    existing["last_used"] = datetime.now().isoformat()
                    existing["use_count"] = existing.get("use_count", 0) + 1
                    existing["supplier"] = memory_data.get("supplier", existing.get("supplier", ""))
                else:
                    # 添加新记忆
                    memory_data["status"] = "saved"
                    self.memories[memory_key] = memory_data
                
                committed_count += 1
            
            # 清空待保存记忆
            self.pending_memories.clear()
            
            # 保存到文件
            success = self.save_memories()
            if success:
                print(f"✅ 成功提交 {committed_count} 个颜色记忆")
            else:
                print(f"❌ 提交颜色记忆失败")
            
            return success
        except Exception as e:
            print(f"❌ 提交待保存记忆失败: {str(e)}")
            return False
    
    def find_matching_memory(self, sku_code: str, color_spec: str) -> Optional[Dict]:
        """查找匹配的记忆"""
        try:
            memory_key = self.generate_memory_key(sku_code, color_spec)
            
            # 首先检查待保存记忆
            if memory_key in self.pending_memories:
                return self.pending_memories[memory_key].copy()
            
            # 然后检查已保存记忆
            if memory_key in self.memories:
                memory = self.memories[memory_key].copy()
                # 更新使用统计
                memory["use_count"] = memory.get("use_count", 0) + 1
                memory["last_used"] = datetime.now().isoformat()
                self.memories[memory_key] = memory
                return memory
            
            return None
        except Exception as e:
            print(f"❌ 查找匹配记忆失败: {str(e)}")
            return None
    
    def delete_memory(self, memory_key: str) -> bool:
        """删除指定记忆"""
        try:
            deleted = False
            
            # 从待保存记忆中删除
            if memory_key in self.pending_memories:
                del self.pending_memories[memory_key]
                deleted = True
            
            # 从已保存记忆中删除
            if memory_key in self.memories:
                del self.memories[memory_key]
                deleted = True
                self.save_memories()
            
            if deleted:
                print(f"✅ 删除记忆: {memory_key}")
            else:
                print(f"⚠️ 记忆不存在: {memory_key}")
            
            return deleted
        except Exception as e:
            print(f"❌ 删除记忆失败: {str(e)}")
            return False
    
    def get_all_memories(self) -> List[Dict]:
        """获取所有记忆列表"""
        try:
            all_memories = []
            
            # 添加已保存的记忆
            for key, memory in self.memories.items():
                memory_copy = memory.copy()
                memory_copy["memory_key"] = key
                memory_copy["status"] = "saved"
                all_memories.append(memory_copy)
            
            # 添加待保存的记忆
            for key, memory in self.pending_memories.items():
                memory_copy = memory.copy()
                memory_copy["memory_key"] = key
                memory_copy["status"] = "pending"
                all_memories.append(memory_copy)
            
            # 按创建时间排序（最新的在前）
            all_memories.sort(key=lambda x: x.get("created_time", ""), reverse=True)
            
            return all_memories
        except Exception as e:
            print(f"❌ 获取记忆列表失败: {str(e)}")
            return []
    
    def get_memory_statistics(self) -> Dict:
        """获取记忆统计信息"""
        try:
            total_saved = len(self.memories)
            total_pending = len(self.pending_memories)
            total_memories = total_saved + total_pending
            
            # 计算使用频率统计
            use_counts = [memory.get("use_count", 0) for memory in self.memories.values()]
            avg_use_count = sum(use_counts) / len(use_counts) if use_counts else 0
            
            # 最近使用的记忆
            recent_memories = []
            for key, memory in self.memories.items():
                if memory.get("last_used"):
                    recent_memories.append({
                        "key": key,
                        "last_used": memory["last_used"],
                        "use_count": memory.get("use_count", 0)
                    })
            
            recent_memories.sort(key=lambda x: x["last_used"], reverse=True)
            
            return {
                "total_memories": total_memories,
                "saved_memories": total_saved,
                "pending_memories": total_pending,
                "average_use_count": round(avg_use_count, 2),
                "most_used_memories": sorted(recent_memories, key=lambda x: x["use_count"], reverse=True)[:5],
                "recent_memories": recent_memories[:5]
            }
        except Exception as e:
            print(f"❌ 获取记忆统计失败: {str(e)}")
            return {}
    
    def export_memories(self, export_file: str) -> bool:
        """导出记忆数据"""
        try:
            export_data = {
                "export_time": datetime.now().isoformat(),
                "total_memories": len(self.memories),
                "memories": self.memories,
                "version": "1.0"
            }
            
            with open(export_file, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 导出记忆数据到: {export_file}")
            return True
        except Exception as e:
            print(f"❌ 导出记忆数据失败: {str(e)}")
            return False
    
    def import_memories(self, import_file: str, merge: bool = True) -> bool:
        """导入记忆数据"""
        try:
            if not os.path.exists(import_file):
                print(f"❌ 导入文件不存在: {import_file}")
                return False
            
            with open(import_file, "r", encoding="utf-8") as f:
                import_data = json.load(f)
            
            imported_memories = import_data.get("memories", {})
            
            if merge:
                # 合并模式：保留现有记忆，添加新记忆
                imported_count = 0
                for key, memory in imported_memories.items():
                    if key not in self.memories:
                        self.memories[key] = memory
                        imported_count += 1
                
                print(f"✅ 合并导入 {imported_count} 个新记忆")
            else:
                # 替换模式：完全替换现有记忆
                self.memories = imported_memories
                print(f"✅ 替换导入 {len(imported_memories)} 个记忆")
            
            # 保存到文件
            return self.save_memories()
        except Exception as e:
            print(f"❌ 导入记忆数据失败: {str(e)}")
            return False
    
    def clear_all_memories(self) -> bool:
        """清空所有记忆"""
        try:
            self.memories.clear()
            self.pending_memories.clear()
            success = self.save_memories()
            if success:
                print("✅ 已清空所有颜色记忆")
            return success
        except Exception as e:
            print(f"❌ 清空记忆失败: {str(e)}")
            return False
