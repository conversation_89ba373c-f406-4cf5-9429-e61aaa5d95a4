"""
UI常量配置 - 统一管理界面常量
"""


class UIConstants:
    """UI常量配置类"""
    
    # 窗口尺寸
    WINDOW_WIDTH = 1400
    WINDOW_HEIGHT = 900
    WINDOW_MIN_WIDTH = 1200
    WINDOW_MIN_HEIGHT = 800
    
    # 按钮尺寸
    BUTTON_HEIGHT = 40
    BUTTON_MIN_HEIGHT = 35
    BUTTON_MIN_WIDTH = 120
    SMALL_BUTTON_HEIGHT = 30
    
    # 分割器尺寸
    MAIN_SPLITTER_SIZES = [700, 800, 700]  # 左侧图像预览:中间表格:右侧确认 (左侧固定700px)
    CONFIG_SPLITTER_SIZES = [300, 800]     # 配置页面分割
    
    # 间距和边距
    LAYOUT_MARGIN = 10
    LAYOUT_SPACING = 10
    SMALL_MARGIN = 5
    SMALL_SPACING = 5
    GROUP_SPACING = 10
    BUTTON_SPACING = 10
    
    # 字体设置
    FONT_FAMILY = "'Microsoft YaHei', 'SimHei', sans-serif"
    FONT_SIZE_NORMAL = 12
    FONT_SIZE_SMALL = 11
    FONT_SIZE_LARGE = 13
    FONT_SIZE_TITLE = 14
    
    # 颜色配置
    class Colors:
        # 主色调
        PRIMARY = "#0071e3"
        PRIMARY_HOVER = "#005bb5"
        PRIMARY_PRESSED = "#004080"
        
        # 状态颜色
        SUCCESS = "#02e87d"
        WARNING = "#ff8c00"
        DANGER = "#d13438"
        INFO = "#0071e3"
        
        # 背景颜色
        BACKGROUND_MAIN = "#000000"
        BACKGROUND_SECONDARY = "#161617"
        BACKGROUND_INPUT = "#161617"
        BACKGROUND_DISABLED = "#3a3a3a"
        
        # 文本颜色
        TEXT_PRIMARY = "#ffffff"
        TEXT_SECONDARY = "#cccccc"
        TEXT_DISABLED = "#888888"
        
        # 边框颜色
        BORDER_NORMAL = "#3a3a3a"
        BORDER_FOCUS = "#0071e3"
        BORDER_HOVER = "#4a4a4a"
    
    # 表格配置
    class Table:
        # 表格列宽（优化为980px总宽度）
        COLUMN_WIDTH_TINY = 40     # 行数列（缩小）
        COLUMN_WIDTH_SMALL = 70    # 退货率列（紧凑）
        COLUMN_WIDTH_MEDIUM = 80   # 数量、单价、小计、利润列
        COLUMN_WIDTH_LARGE = 120   # 款号列（缩小）
        COLUMN_WIDTH_XLARGE = 140  # 颜色规格列（缩小）
        COLUMN_WIDTH_STATUS = 150  # 状态列（缩小）
        
        # 表格行高
        ROW_HEIGHT = 30
        HEADER_HEIGHT = 35
        
        # 表格头标签（删除7退列，只保留15退和30退）
        PICKUP_HEADERS = ["行数", "款号", "颜色规格", "数量", "单价", "小计", "状态", "利润", "15退", "30退"]
        RETURN_HEADERS = ["行数", "款号", "颜色规格", "数量", "单价", "小计", "状态", "利润", "15退", "30退"]
    
    # 图标和表情符号
    class Icons:
        # 功能图标
        HOME = "🏠"
        MAIN = "🏠"  # 主操作页面图标
        AI = "🤖"
        SEARCH = "🔍"
        SETTINGS = "⚙️"
        LOG = "📋"
        
        # 操作图标
        UPLOAD = "📁"
        FOLDER = "📁"  # 文件夹图标
        PASTE = "📋"
        DELETE = "🗑️"
        REFRESH = "🔄"
        SAVE = "💾"
        CLEAR = "🗑️"  # 清空图标
        SPLIT = "✂️"  # 分割图标
        COPY = "📋"   # 复制图标
        EXPORT = "📤"  # 导出图标
        AI_RESPONSE = "💬"  # AI响应图标
        
        # 状态图标
        SUCCESS = "✅"
        ERROR = "❌"
        WARNING = "⚠️"
        INFO = "ℹ️"
        LOADING = "🔍"
        
        # ERP状态图标
        PRICE_UP = "📈"
        PRICE_DOWN = "📉"
        NO_CHANGE = "✅"
        NEW_PRICE = "✅"
        
        # 商品类型图标
        PICKUP = "🛒"
        RETURN = "↩️"
        
        # 其他图标
        TARGET = "🎯"
        MONEY = "💰"
        CHART = "📊"
        IMAGE = "🖼️"
        FILE = "📄"
    
    # 消息文本
    class Messages:
        # 状态消息
        READY = "就绪"
        PROCESSING = "正在处理..."
        COMPLETED = "处理完成"
        ERROR = "处理出错"
        
        # 操作消息
        FILE_SELECTED = "文件已选择"
        IMAGE_PASTED = "图像已粘贴"
        DATA_SAVED = "数据已保存"
        CONFIG_LOADED = "配置已加载"
        
        # 错误消息
        NO_FILE_SELECTED = "请先选择文件"
        NO_DATA_FOUND = "未找到数据"
        NETWORK_ERROR = "网络连接错误"
        API_ERROR = "API调用失败"
    
    # 线程配置
    class Thread:
        # 超时设置
        STOP_TIMEOUT = 3000      # 3秒
        TERMINATE_TIMEOUT = 1000 # 1秒
        
        # 延时设置
        QUERY_DELAY = 500        # ERP查询延时(毫秒)
        UPDATE_DELAY = 300       # 更新延时(毫秒)
    
    # 文件配置
    class File:
        # 支持的文件类型
        IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff']
        PDF_EXTENSIONS = ['.pdf']
        
        # 文件对话框过滤器
        IMAGE_FILTER = "图像文件 (*.jpg *.jpeg *.png *.gif *.bmp *.webp *.tiff)"
        PDF_FILTER = "PDF文件 (*.pdf)"
        ALL_FILTER = "所有文件 (*.*)"
        
        # 文件大小限制
        MAX_IMAGE_SIZE = 50 * 1024 * 1024  # 50MB
        MAX_PDF_SIZE = 100 * 1024 * 1024   # 100MB
    
    # 动画配置
    class Animation:
        # 动画时长
        FADE_DURATION = 300
        SLIDE_DURATION = 250
        BUTTON_HOVER_DURATION = 150
        
        # 缓动曲线
        EASE_IN_OUT = "ease-in-out"
        EASE_IN = "ease-in"
        EASE_OUT = "ease-out"
    
    # 布局配置
    class Layout:
        # 圆角半径
        BORDER_RADIUS_SMALL = 4
        BORDER_RADIUS_MEDIUM = 6
        BORDER_RADIUS_LARGE = 8
        
        # 阴影配置
        SHADOW_BLUR = 10
        SHADOW_OFFSET = 2
        
        # 透明度
        OPACITY_DISABLED = 0.5
        OPACITY_HOVER = 0.8
        OPACITY_PRESSED = 0.6


class StyleHelper:
    """样式辅助类"""
    
    @staticmethod
    def create_button_style(button_type="primary", height=None, width=None):
        """创建按钮样式"""
        from .ui_style_manager import UIStyleManager
        
        base_style = UIStyleManager.get_button_style(button_type)
        
        # 添加自定义尺寸
        if height or width:
            size_style = "QPushButton {"
            if height:
                size_style += f"min-height: {height}px; max-height: {height}px;"
            if width:
                size_style += f"min-width: {width}px;"
            size_style += "}"
            base_style += size_style
        
        return base_style
    
    @staticmethod
    def create_status_color(status_text):
        """根据状态文本返回对应颜色"""
        if "✅" in status_text:
            return UIConstants.Colors.SUCCESS
        elif "❌" in status_text:
            return UIConstants.Colors.DANGER
        elif "⚠️" in status_text:
            return UIConstants.Colors.WARNING
        elif "📈" in status_text:
            return UIConstants.Colors.DANGER  # 价格上涨用红色字体
        elif "📉" in status_text:
            return UIConstants.Colors.SUCCESS  # 价格下降用绿色字体
        else:
            return UIConstants.Colors.TEXT_PRIMARY
    
    @staticmethod
    def format_price_display(price, show_formula=True):
        """格式化价格显示"""
        try:
            price_float = float(price)
            if show_formula and price_float >= 100:
                return f"{price_float:.0f}-100={price_float-100:.0f}"
            else:
                return f"¥{price_float:.1f}"
        except (ValueError, TypeError):
            return str(price) if price else "-"
    
    @staticmethod
    def format_profit_display(sale_price, cost_price):
        """格式化利润显示"""
        try:
            sale = float(sale_price) if sale_price else 0
            cost = float(cost_price) if cost_price else 0
            
            if sale > 0 and cost > 0:
                profit = sale - 100 - cost  # 减去100的公式
                margin = (profit / (sale - 100)) * 100 if sale > 100 else 0
                
                color = UIConstants.Colors.DANGER if profit < 20 else UIConstants.Colors.TEXT_PRIMARY
                return f"利润：¥{profit:.1f} 毛利：{margin:.1f}%", color
            else:
                return "-", UIConstants.Colors.TEXT_SECONDARY
        except (ValueError, TypeError):
            return "-", UIConstants.Colors.TEXT_SECONDARY
