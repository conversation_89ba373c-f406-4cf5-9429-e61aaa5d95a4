"""
统一的线程安全Logger类
重构自main_gui_controller.py，消除重复的Logger定义
"""

from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QTextCursor


class ThreadSafeLogger(QObject):
    """线程安全的日志类"""
    log_signal = pyqtSignal(str)  # 日志信号，确保线程安全

    def __init__(self, callback=None):
        super().__init__()
        self.callback = callback
        self.max_log_lines = 1000  # 最大日志行数
        self.log_signal.connect(self._handle_log_message)

    def _handle_log_message(self, message: str):
        """处理日志消息（在主线程中执行）"""
        if self.callback:
            self.callback(message)

    def log(self, message: str, level: str = "INFO"):
        """记录日志（线程安全）"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"

        # 使用信号确保在主线程中更新UI
        self.log_signal.emit(log_entry)

    def info(self, message: str):
        """信息日志"""
        self.log(message, "INFO")

    def warning(self, message: str):
        """警告日志"""
        self.log(message, "WARNING")

    def error(self, message: str):
        """错误日志"""
        self.log(message, "ERROR") 