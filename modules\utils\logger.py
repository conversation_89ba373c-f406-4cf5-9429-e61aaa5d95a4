"""
统一的线程安全Logger类
重构自main_gui_controller.py，消除重复的Logger定义
支持日志级别控制，优化终端输出
"""

from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtGui import QTextCursor
from enum import Enum
from typing import Optional

class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40

class ThreadSafeLogger(QObject):
    """线程安全的日志类，支持日志级别控制"""
    log_signal = pyqtSignal(str)  # 日志信号，确保线程安全

    # 全局日志实例
    _instance = None

    @classmethod
    def get_instance(cls, callback=None):
        """获取全局日志实例（单例模式）"""
        if cls._instance is None:
            cls._instance = ThreadSafeLogger(callback)
        return cls._instance

    def __init__(self, callback=None):
        super().__init__()
        self.callback = callback
        self.max_log_lines = 1000  # 最大日志行数
        self.log_signal.connect(self._handle_log_message)
        self.log_level = LogLevel.INFO  # 默认日志级别
        self.console_output = True  # 是否输出到控制台

    def set_level(self, level: LogLevel):
        """设置日志级别"""
        self.log_level = level

    def get_level(self) -> LogLevel:
        """获取当前日志级别"""
        return self.log_level

    def set_console_output(self, enabled: bool):
        """设置是否输出到控制台"""
        self.console_output = enabled

    def _handle_log_message(self, message: str):
        """处理日志消息（在主线程中执行）"""
        if self.callback:
            self.callback(message)

        # 输出到控制台
        if self.console_output:
            print(message)

    def log(self, message: str, level: str = "INFO", level_enum: LogLevel = None):
        """记录日志（线程安全）"""
        # 检查日志级别
        if level_enum is None:
            # 从字符串转换为枚举
            level_enum = {
                "DEBUG": LogLevel.DEBUG,
                "INFO": LogLevel.INFO,
                "WARNING": LogLevel.WARNING,
                "ERROR": LogLevel.ERROR,
                "SUCCESS": LogLevel.INFO  # SUCCESS使用INFO级别
            }.get(level.upper(), LogLevel.INFO)

        # 如果日志级别低于设置级别，则不记录
        if level_enum.value < self.log_level.value:
            return

        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"

        # 使用信号确保在主线程中更新UI
        self.log_signal.emit(log_entry)

    def debug(self, message: str):
        """调试日志 - 仅在DEBUG级别显示"""
        self.log(message, "DEBUG", LogLevel.DEBUG)

    def info(self, message: str):
        """信息日志"""
        self.log(message, "INFO", LogLevel.INFO)

    def warning(self, message: str):
        """警告日志"""
        self.log(message, "WARNING", LogLevel.WARNING)

    def error(self, message: str):
        """错误日志"""
        self.log(message, "ERROR", LogLevel.ERROR)

    def success(self, message: str):
        """成功日志 - 使用INFO级别，添加SUCCESS标记"""
        self.log(f"[SUCCESS] {message}", "INFO", LogLevel.INFO)

# 全局便捷函数
def get_logger(callback=None) -> ThreadSafeLogger:
    """获取全局日志实例"""
    return ThreadSafeLogger.get_instance(callback)

def set_log_level(level: LogLevel):
    """设置全局日志级别"""
    logger = get_logger()
    logger.set_level(level)

def debug(message: str):
    """调试日志"""
    get_logger().debug(message)

def info(message: str):
    """信息日志"""
    get_logger().info(message)

def warning(message: str):
    """警告日志"""
    get_logger().warning(message)

def error(message: str):
    """错误日志"""
    get_logger().error(message)

def success(message: str):
    """成功日志"""
    get_logger().success(message)

# 兼容性函数 - 保持与现有代码的兼容性
def log_message(message: str, level: str = "INFO"):
    """兼容性日志函数"""
    logger = get_logger()
    logger.log(message, level)