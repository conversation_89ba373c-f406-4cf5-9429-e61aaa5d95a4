"""
AI处理界面 - 负责AI解析相关界面逻辑
重构自原始pyqt5_main_gui.py的AI处理部分
"""

import os
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *

from modules.ui_constants import UIConstants


class AIProcessorUI:
    """AI处理界面管理器"""
    
    def __init__(self, main_window):
        self.main_window = main_window
        
    def create_ai_process_buttons(self, layout):
        """创建AI处理按钮区域"""
        button_group = QGroupBox()  # 移除标题，按YUE哥要求简化界面
        button_group.setStyleSheet("QGroupBox { border: none; margin: 0px; padding: 0px; }")
        layout.addWidget(button_group)

        button_layout = QVBoxLayout(button_group)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(10)

        # AI处理按钮 - 只保留这一个按钮
        self.ai_process_btn = QPushButton("AI解析票据")  # 去掉emoji图标
        self.ai_process_btn.clicked.connect(self.start_ai_processing)
        self.ai_process_btn.setMinimumHeight(UIConstants.BUTTON_HEIGHT)
        self.ai_process_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 14px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
            }
            QPushButton:hover {
                background-color: #218838;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
            QPushButton:disabled {
                background-color: #6c757d;
            }
        """)
        button_layout.addWidget(self.ai_process_btn)

        # 添加文件操作按钮（移动到AI解析票据按钮下面）
        file_ops_layout = QHBoxLayout()

        # 上传图片按钮（原选择文件按钮）
        self.upload_btn = QPushButton(f"{UIConstants.Icons.FOLDER} 上传图片")
        self.upload_btn.clicked.connect(self.main_window.select_files)
        self.upload_btn.setMinimumHeight(UIConstants.BUTTON_HEIGHT)
        file_ops_layout.addWidget(self.upload_btn)

        # 粘贴图像按钮
        self.paste_btn = QPushButton(f"{UIConstants.Icons.PASTE} 粘贴图像")
        self.paste_btn.clicked.connect(self.main_window.paste_image)
        self.paste_btn.setMinimumHeight(UIConstants.BUTTON_HEIGHT)
        file_ops_layout.addWidget(self.paste_btn)

        button_layout.addLayout(file_ops_layout)

        # 保存按钮引用到主窗口
        self.main_window.ai_process_btn = self.ai_process_btn
        self.main_window.upload_btn = self.upload_btn
        self.main_window.paste_btn = self.paste_btn
        # 注意：不再创建ai_process_selected_btn、progress_bar和status_label

    def create_ai_response_tab(self):
        """创建AI响应标签页"""
        ai_response_tab = QWidget()
        self.main_window.tab_widget.addTab(ai_response_tab, f"{UIConstants.Icons.AI_RESPONSE} AI响应")

        ai_response_layout = QVBoxLayout(ai_response_tab)
        ai_response_layout.setContentsMargins(5, 5, 5, 5)

        # AI响应文本区域
        self.main_window.ai_response_text = QTextEdit()
        self.main_window.ai_response_text.setReadOnly(True)
        self.main_window.ai_response_text.setFont(QFont("Consolas", 10))
        self.main_window.ai_response_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #444444;
            }
        """)
        ai_response_layout.addWidget(self.main_window.ai_response_text)

        # AI响应操作按钮
        ai_button_layout = QHBoxLayout()

        self.copy_ai_response_btn = QPushButton(f"{UIConstants.Icons.COPY} 复制响应")
        self.copy_ai_response_btn.clicked.connect(self.copy_ai_response)
        ai_button_layout.addWidget(self.copy_ai_response_btn)

        ai_button_layout.addStretch()
        ai_response_layout.addLayout(ai_button_layout)

    # AI处理方法（从main_gui_controller移过来）
    def start_ai_processing(self):
        """开始AI处理"""
        if self.main_window.processing:
            self.main_window.log_message("正在处理中，请稍候...", "WARNING")
            return

        # 获取需要处理的文件（智能选择）
        files_to_process = self.get_files_for_ai_processing()
        if not files_to_process:
            self.main_window.log_message("没有文件需要处理", "WARNING")
            return

        # 检查配置
        if not self.validate_ai_config():
            self.main_window.log_message("请先配置AI服务", "ERROR")
            # 切换到配置页面
            for i in range(self.main_window.tab_widget.count()):
                if "配置设置" in self.main_window.tab_widget.tabText(i):
                    self.main_window.tab_widget.setCurrentIndex(i)
                    break
            return

        self.start_ai_processing_internal(files_to_process)

    def start_ai_processing_selected(self):
        """开始AI处理选定图像"""
        if self.main_window.processing:
            self.main_window.log_message("正在处理中，请稍候...", "WARNING")
            return

        # 获取当前选定的图像
        selected_file = self.get_selected_image_file()
        if not selected_file:
            self.main_window.log_message("请先选择要解析的图像", "WARNING")
            return

        files_to_process = [selected_file]

        # 检查AI配置
        if not self.validate_ai_config():
            self.main_window.log_message("请先配置AI服务", "ERROR")
            return

        self.start_ai_processing_internal(files_to_process)

    def start_ai_processing_internal(self, files_to_process):
        """内部AI处理启动方法"""
        try:
            self.main_window.processing = True
            self.ai_process_btn.setEnabled(False)
            self.main_window.status_bar.showMessage("正在处理...")

            # 🔥 新增：显示统一进度条
            self.main_window.show_unified_progress("AI解析票据中...")

            # 启动AI处理线程
            from modules.threads import AIProcessingThread
            self.main_window.ai_thread = AIProcessingThread(files_to_process, self.get_ai_config(), self.get_prompt())
            self.main_window.ai_thread.progress_updated.connect(self.on_ai_progress_updated)
            self.main_window.ai_thread.result_ready.connect(self.on_ai_result_ready)
            self.main_window.ai_thread.finished.connect(self.on_ai_processing_finished)
            self.main_window.ai_thread.start()

        except Exception as e:
            self.main_window.log_message(f"启动AI处理失败: {str(e)}", "ERROR")
            self.on_ai_processing_finished()

    def get_files_for_ai_processing(self):
        """获取需要AI处理的文件（智能选择，跳过已解析，分割文件优先）"""
        try:
            all_files = self.main_window.file_manager.get_file_list()
            if not all_files:
                return []

            # 获取已解析的文件列表
            parsed_files = set()
            if hasattr(self.main_window, 'stored_results') and self.main_window.stored_results:
                for file_path in self.main_window.stored_results.keys():
                    if not file_path.startswith('[MERGED]_'):  # 排除合并结果
                        parsed_files.add(file_path)

            # 分类文件：原始文件和分割文件
            original_files = []
            segment_files = []

            for file_path in all_files:
                filename = os.path.basename(file_path)
                # 🔥 关键逻辑：检测分割文件模式
                if ('_segment_' in filename or 
                    filename.endswith(('_1.jpg', '_2.jpg', '_3.jpg', '_4.jpg', '_5.jpg')) or
                    any(filename.endswith(f'_{i}.jpg') for i in range(1, 10))):
                    segment_files.append(file_path)
                else:
                    original_files.append(file_path)

            # 🔥 智能选择逻辑：
            # 1. 如果有分割文件，只处理分割文件，不处理对应的原始文件
            # 2. 如果没有分割文件，处理原始文件
            # 3. 跳过已解析的文件

            files_to_process = []
            processed_originals = set()
            skipped_parsed = []

            # 处理分割文件（优先）
            for segment_file in segment_files:
                # 检查是否已解析
                if segment_file in parsed_files:
                    skipped_parsed.append(segment_file)
                    continue

                files_to_process.append(segment_file)

                # 🔥 关键：找到对应的原始文件并标记为已处理
                segment_name = os.path.basename(segment_file)
                original_name = None
                
                if '_segment_' in segment_name:
                    original_name = segment_name.split('_segment_')[0]
                elif segment_name.endswith(('_1.jpg', '_2.jpg', '_3.jpg', '_4.jpg', '_5.jpg')):
                    original_name = segment_name.rsplit('_', 1)[0]
                else:
                    # 处理其他数字后缀模式
                    parts = segment_name.rsplit('_', 1)
                    if len(parts) == 2 and parts[1].replace('.jpg', '').isdigit():
                        original_name = parts[0]

                if original_name:
                    # 查找匹配的原始文件
                    for original_file in original_files:
                        original_basename = os.path.splitext(os.path.basename(original_file))[0]
                        display_name = self.main_window.get_display_name_for_file(original_file)

                        if original_basename == original_name or display_name == original_name:
                            processed_originals.add(original_file)
                            self.main_window.log_message(f"🔄 跳过原图 {original_name}，使用分割图像")
                            break

            # 处理未被分割的原始文件
            for original_file in original_files:
                if original_file not in processed_originals:
                    # 检查是否已解析
                    if original_file in parsed_files:
                        skipped_parsed.append(original_file)
                        continue

                    files_to_process.append(original_file)

            # 日志信息
            total_files = len(all_files)
            process_files = len(files_to_process)
            skipped_parsed_count = len(skipped_parsed)
            skipped_originals_count = len(processed_originals)

            self.main_window.log_message(f"🎯 智能选择：处理 {process_files}/{total_files} 个文件")
            
            if skipped_originals_count > 0:
                self.main_window.log_message(f"⏭️ 跳过原图：{skipped_originals_count} 个（有分割图像）")
            
            if skipped_parsed_count > 0:
                skipped_names = [os.path.basename(f) for f in skipped_parsed[:3]]
                self.main_window.log_message(f"⏭️ 跳过已解析：{len(skipped_parsed)} 个 {skipped_names}{'...' if len(skipped_parsed) > 3 else ''}")

            return files_to_process

        except Exception as e:
            self.main_window.log_message(f"文件选择失败: {str(e)}", "ERROR")
            return all_files

    def get_selected_image_file(self):
        """获取当前选定的图像文件"""
        try:
            # 检查图像管理器中是否有当前选定的图像
            if hasattr(self.main_window, 'image_manager') and hasattr(self.main_window.image_manager, 'current_image_path'):
                if self.main_window.image_manager.current_image_path:
                    return self.main_window.image_manager.current_image_path

            # 检查是否有当前预览的图像（向后兼容）
            if hasattr(self.main_window.image_manager, 'current_image_path') and self.main_window.image_manager.current_image_path:
                return self.main_window.image_manager.current_image_path

            # 如果没有选中的，返回第一个文件
            all_files = self.main_window.file_manager.get_file_list()
            if all_files:
                return all_files[0]

            return None
        except Exception as e:
            self.main_window.log_message(f"获取选定图像失败: {str(e)}", "ERROR")
            return None

    def validate_ai_config(self):
        """验证AI配置"""
        try:
            config = self.main_window.config_manager.get_ai_config()
            return (config.get("api_key", "").strip() and
                    config.get("api_url", "").strip())
        except Exception as e:
            self.main_window.log_message(f"验证AI配置失败: {str(e)}", "ERROR")
            return False

    def get_ai_config(self):
        """获取AI配置"""
        try:
            return self.main_window.config_manager.get_ai_config()
        except Exception as e:
            self.main_window.log_message(f"获取AI配置失败: {str(e)}", "ERROR")
            return {}

    def get_prompt(self):
        """获取提示词"""
        try:
            return self.main_window.config_manager.get_prompt()
        except Exception as e:
            self.main_window.log_message(f"获取提示词失败: {str(e)}", "ERROR")
            return ""

    def on_ai_progress_updated(self, progress, message):
        """AI处理进度更新"""
        self.main_window.status_bar.showMessage(message)
        self.main_window.log_message(message)

        # 🔥 新增：更新统一进度条
        # 假设总文件数可以从线程获取，这里简化处理
        if hasattr(self.main_window, 'ai_thread') and self.main_window.ai_thread:
            total_files = len(self.main_window.ai_thread.files)
            progress_percent = int((progress / total_files) * 100) if total_files > 0 else 0
            self.main_window.update_unified_progress(progress_percent, f"AI解析: {message}")

    def on_ai_result_ready(self, file_path, result):
        """AI结果准备就绪"""
        try:
            if result.get("success", True):
                self.main_window.log_message(f"✅ AI解析完成: {self.main_window.get_display_name_for_file(file_path)}")
                
                # 只添加到解析结果列表，不自动添加到商品明细表格
                # 用户需要手动选择解析结果才能添加到商品明细表格，避免不同供应商数据自动合并
                self.main_window.table_ui_manager.add_file_result_to_table(file_path, result)
                
                # 显示AI响应
                if hasattr(self.main_window, 'ai_response_text') and result:
                    import json
                    response_text = f"文件: {self.main_window.get_display_name_for_file(file_path)}\n"
                    if result.get("success") and "data" in result:
                        # 显示解析后的结构化数据
                        ticket_data = result["data"]
                        response_text += f"供应商: {ticket_data.get('supplier', '未知')}\n"
                        response_text += f"日期: {ticket_data.get('date', '未知')}\n"
                        response_text += f"类型: {ticket_data.get('type', '未知')}\n"
                        response_text += f"商品数量: {len(ticket_data.get('items', []))}\n"
                        response_text += f"原始AI响应: {result.get('ai_response', '')[:200]}...\n"
                    else:
                        response_text += f"结果: {str(result)}\n"
                    response_text += "-" * 50 + "\n"
                    self.main_window.ai_response_text.append(response_text)
            else:
                error_msg = result.get("error", "未知错误")
                self.main_window.log_message(f"❌ AI解析失败: {self.main_window.get_display_name_for_file(file_path)} - {error_msg}", "ERROR")
                
        except Exception as e:
            self.main_window.log_message(f"处理AI结果失败: {str(e)}", "ERROR")

    def on_ai_processing_finished(self):
        """AI处理完成"""
        self.main_window.processing = False
        self.ai_process_btn.setEnabled(True)
        self.main_window.status_bar.showMessage("处理完成")
        self.main_window.log_message("AI处理完成")

        # 🔥 新增：隐藏统一进度条
        self.main_window.hide_unified_progress()

    def copy_ai_response(self):
        """复制AI响应"""
        if hasattr(self.main_window, 'ai_response_text'):
            clipboard = QApplication.clipboard()
            clipboard.setText(self.main_window.ai_response_text.toPlainText())
            self.main_window.log_message("AI响应已复制到剪贴板")

    def clear_ai_response(self):
        """清空AI响应"""
        if hasattr(self.main_window, 'ai_response_text'):
            self.main_window.ai_response_text.clear()
            self.main_window.log_message("AI响应已清空") 