#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ERP系统集成模块
智能认证策略，支持自动和手动认证方式
"""

import requests
import json
import time
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class ERPIntegration:
    """ERP系统集成器 - 智能认证策略"""
    
    def __init__(self, log_callback: Optional[Callable] = None, config: Optional[Dict[str, Any]] = None):
        # ERP API基础配置
        self.base_url = "https://apiweb.erp321.com/webapi"
        self.owner_co_id = "13881863"
        self.authorize_co_id = "13881863"
        self.log_callback = log_callback or print

        # 加载优化配置
        self.optimization_config = self._load_optimization_config(config)
        
        # 内置有效cookies（从测试中验证有效）
        self.cookies = {
            'u_lastLoginType': 'ap',
            '_sid18707109': '7D2FC1BBBBE93BE5B087C3A4E40BD1C1',
            '3AB9D23F7A4B3CSS': 'jdd03XIHL6KA5RO36ATQZJPMOOJCP37B4WBLKVGRWEHZL7IF7TVA4XQ7G5JV6VQYUEIIGPO4XFJLCD2D45HZYNJXIOXH4Q4AAAAMW4O2I3FAAAAAADHHAYIPOG45DHIX',
            'j_d_3': 'XIHL6KA5RO36ATQZJPMOOJCP37B4WBLKVGRWEHZL7IF7TVA4XQ7G5JV6VQYUEIIGPO4XFJLCD2D45HZYNJXIOXH4Q4',
            'u_ssi': '',
            'u_drp': '-1',
            'jump_env': 'www',
            'isLogin': 'true',
            'tmp_gray': '1',
            'jump_isgray': '0',
            'u_shop': '-1',
            '3AB9D23F7A4B3C9B': 'XIHL6KA5RO36ATQZJPMOOJCP37B4WBLKVGRWEHZL7IF7TVA4XQ7G5JV6VQYUEIIGPO4XFJLCD2D45HZYNJXIOXH4Q4',
            'u_name': 'MORAN',
            'u_lid': '13763365321',
            'u_co_name': '%e8%8c%89%e7%84%b6moran',
            'v_d_144': '1749521443437_fa0ab0c2bd8461a7a21193526a087093',
            'u_cid': '133939950470094677',
            'u_r': '11%2c12%2c13%2c14%2c23%2c27%2c34%2c39%2c40%2c41',
            'u_sso_token': 'CS@5b482b27dd2f404b99c4c5717b43161a',
            'u_id': '18707109',
            'u_co_id': '13881863',
            'p_50': '073DC188643452DC75897E865BC6EC4F638851470470101666%7c13881863',
            'u_env': 'www',
            'u_isTPWS': '2',
            'u_json': '%7b%22t%22%3a%222025-6-11+11%3a22%3a44%22%2c%22co_type%22%3a%22%e6%a0%87%e5%87%86%e5%95%86%e5%ae%b6%22%2c%22proxy%22%3anull%2c%22ug_id%22%3a%22%22%2c%22dbc%22%3a%221232%22%2c%22tt%22%3a%222%22%2c%22apps%22%3a%221.152.160%22%2c%22pwd_valid%22%3a%220%22%2c%22ssi%22%3a%22%22%2c%22sign%22%3a%224396921.2582087C4D9847D6858F8ECB809D9CF0%2c2400698c552939ed0bbd5759c42e75df%22%7d',
            'acw_tc': 'ac11000117496329947262325e00569b4f63ca5b68bd4a73af8378694e03ba',
            'tfstk': 'g7foGvZlqTJ7vVrvHsO7QtpCvr2veQOBCMhpvBKU3n-j20hR8XvFReTezwr5x6x2ap6Jv3KHtwsgklFT6a_WOprTX5BkbTSDOHPpT-dq12YKKlFT6a8AjFQYX6B7cJxX8B8rab74oULBTURe4rx2-FME4MSUon8MJe8ETH823e-2YBRFYZzDJn8yTBlWnY-OTO54D1sJL7-5NsYkEh7uCXlhifpkba-mTl5DrktNzncET3le4vQcyuc1kQ1GjFI8gbjG5ssDuGP3mBsfLM8h05ipNOI51KbbQj7kZnpNLK4zaZAkqdCXIVHXZ9S5tL6oRP_2ai66fLybcEfR6px6nmrPkZxyI9j8cX-RIZSDBsnx6BsfLM8h00jyOxkaofcBuyCmdv9ylExTCSTYlo6mmgU0oAQBUET6Xr4mdv9ylExTorDOOL8X5hC..'
        }
        
        # 请求头
        self.headers = {
            'accept': 'application/json',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'content-type': 'application/json; charset=utf-8',
            'gwfp': '40e7b7781306f7db88f10f172249e42d',
            'origin': 'https://src.erp321.com',
            'priority': 'u=1, i',
            'sec-ch-ua': '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            'u_sso_token;': '',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0',
            'webbox-request-id': 'f1b74048-8527-458e-8c71-1c8e5769b9aa',
            'webbox-route-path': '/erp-web-group/erp-scm-goods/'
        }
        
        # 认证状态
        self.auth_valid = True
        self.last_auth_check = None

        # 性能优化相关
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.session.cookies.update(self.cookies)
        self._setup_session_optimizations()

        # 动态延时控制（从配置加载）
        self.min_delay = self.optimization_config.get('min_delay', 0.1)
        self.max_delay = self.optimization_config.get('max_delay', 2.0)
        self.current_delay = (self.min_delay + self.max_delay) / 2  # 初始延时为中间值
        self.delay_lock = threading.Lock()

        # 并发控制（从配置加载）
        self.max_concurrent_requests = self.optimization_config.get('max_concurrent_requests', 3)

        # 性能统计
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0
        }

        # 延迟加载cookies，避免启动时阻塞
        self._cookies_loaded = False

    def _load_optimization_config(self, config: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """加载优化配置"""
        default_config = {
            'enabled': True,
            'max_concurrent_requests': 3,
            'min_delay': 0.1,
            'max_delay': 2.0,
            'auto_strategy': True
        }

        if config and 'erp_optimization' in config:
            optimization_config = config['erp_optimization']
            # 合并配置，用户配置覆盖默认配置
            for key, value in optimization_config.items():
                if key in default_config:
                    default_config[key] = value

            self.log(f"✅ 已加载ERP优化配置: {optimization_config}")
        else:
            self.log("📋 使用默认ERP优化配置")

        return default_config

    def _setup_session_optimizations(self):
        """设置Session优化配置"""
        # 连接池优化
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,  # 连接池大小
            pool_maxsize=20,      # 最大连接数
            max_retries=3,        # 重试次数
            pool_block=False      # 非阻塞模式
        )

        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)

        # 设置合理的超时
        self.session.timeout = (5, 10)  # (连接超时, 读取超时)

        self.log("✅ Session优化配置已启用")

    def _adjust_delay(self, success: bool, response_time: float):
        """根据请求结果动态调整延时"""
        with self.delay_lock:
            if success and response_time < 1.0:
                # 请求成功且响应快，减少延时
                self.current_delay = max(self.min_delay, self.current_delay * 0.9)
            elif not success or response_time > 3.0:
                # 请求失败或响应慢，增加延时
                self.current_delay = min(self.max_delay, self.current_delay * 1.5)

            # 更新统计信息
            self.request_stats['total_requests'] += 1
            if success:
                self.request_stats['successful_requests'] += 1
            else:
                self.request_stats['failed_requests'] += 1

            # 更新平均响应时间
            total = self.request_stats['total_requests']
            current_avg = self.request_stats['avg_response_time']
            self.request_stats['avg_response_time'] = (current_avg * (total - 1) + response_time) / total

    def load_cookies_from_file(self):
        """从latest_cookies.json文件加载最新的cookies（优化版本）"""
        if self._cookies_loaded:
            return  # 避免重复加载

        try:
            import os
            cookies_file = "latest_cookies.json"
            if os.path.exists(cookies_file):
                # 快速检查文件大小
                file_size = os.path.getsize(cookies_file)
                if file_size > 1024 * 1024:  # 1MB限制
                    self.log(f"⚠️ Cookies文件过大 ({file_size} bytes)，跳过加载")
                    return

                with open(cookies_file, 'r', encoding='utf-8') as f:
                    file_cookies = json.load(f)
                    if file_cookies:
                        self.cookies.update(file_cookies)
                        self.log(f"✅ 已从文件加载 {len(file_cookies)} 个cookies")
                        # 重置认证状态，需要重新验证
                        self.auth_valid = False
                        self.last_auth_check = None
            else:
                self.log("⚠️ latest_cookies.json文件不存在，使用内置cookies")
        except Exception as e:
            self.log(f"⚠️ 加载cookies文件失败: {str(e)}，使用内置cookies")
        finally:
            self._cookies_loaded = True

    def log(self, message: str):
        """日志输出"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_msg = f"[{timestamp}] ERP: {message}"
        if self.log_callback:
            self.log_callback(log_msg)
    
    def check_auth_status(self) -> bool:
        """
        检查认证状态
        通过发送一个简单的API请求来验证cookies是否有效
        """
        # 确保cookies已加载
        if not self._cookies_loaded:
            self.load_cookies_from_file()

        self.log("检查ERP认证状态...")

        try:
            # 使用一个轻量级的API来测试认证
            test_url = f"{self.base_url}/ItemApi/ItemSku/GetPageListV2"
            params = {
                'owner_co_id': self.owner_co_id,
                'authorize_co_id': self.authorize_co_id
            }
            
            test_data = {
                "ip": "",
                "uid": "18707109",
                "coid": "13881863",
                "data": {
                    "page": 1,
                    "limit": 1,
                    "search_text": "test",
                    "search_type": "sku_code"
                }
            }
            
            # 使用requests的json参数，让它自动处理编码
            headers_copy = self.headers.copy()
            headers_copy['content-type'] = 'application/json; charset=utf-8'

            response = requests.post(
                test_url,
                params=params,
                headers=headers_copy,
                cookies=self.cookies,
                json=test_data,  # 使用json参数，自动处理编码
                timeout=10
            )

            # 设置正确的编码以处理中文字符
            response.encoding = 'utf-8'

            if response.status_code == 200:
                result = response.json()
                # 检查是否返回了有效的响应结构
                if 'code' in result and 'data' in result:
                    self.auth_valid = True
                    self.last_auth_check = datetime.now()
                    self.log("ERP认证有效")
                    return True
            
            self.auth_valid = False
            self.log("ERP认证已失效")
            return False
            
        except Exception as e:
            self.log(f"认证检查异常: {str(e)}")
            self.auth_valid = False
            return False
    
    def update_cost_price(self, sku_id: str, new_cost_price: float) -> bool:
        """
        更新商品成本价
        
        Args:
            sku_id: 完整的SKU ID，如 "GD340-9026-衬衫-L"
            new_cost_price: 新的成本价
            
        Returns:
            更新是否成功
        """
        self.log(f"💰 更新成本价: {sku_id} -> {new_cost_price}")
        
        # 检查认证状态
        if not self.auth_valid or not self.check_auth_status():
            self.log("❌ 认证失效，请更新认证信息")
            return False
        
        # 构建更新URL
        update_url = f"{self.base_url}/ItemApi/ItemSku/SetEditOrCreateItemSkuInfoTotal"
        params = {
            'owner_co_id': self.owner_co_id,
            'authorize_co_id': self.authorize_co_id
        }
        
        # 更新请求体
        update_data = {
            "ip": "",
            "uid": "18707109",
            "coid": "13881863",
            "data": {
                "cost_price": new_cost_price,
                "sku_id": sku_id,
                "EditFlds": []
            }
        }
        
        start_time = time.time()
        success = False

        try:
            # 使用Session发送请求（性能优化）
            response = self.session.post(
                update_url,
                params=params,
                json=update_data,
                timeout=(5, 10)  # (连接超时, 读取超时)
            )

            # 设置正确的编码以处理中文字符
            response.encoding = 'utf-8'
            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()

                if result.get('code') == 0 and result.get('data') is True:
                    self.log(f"✅ 成本价更新成功: {sku_id} (耗时: {response_time:.2f}s)")
                    success = True
                    return True
                else:
                    msg = result.get('msg', '未知错误')
                    code = result.get('code', -1)

                    # 🔥 改进：检测token失效并尝试恢复
                    if code == -1 and 'token失效' in msg:
                        self.log(f"🔄 检测到token失效，标记认证无效: {msg}")
                        self.auth_valid = False
                        self.last_auth_check = None

                        # 清理并重新加载cookies
                        self.session.cookies.clear()
                        self.load_cookies_from_file()  # 重新从文件加载最新cookies
                        self.session.cookies.update(self.cookies)

                        self.log(f"🔄 已重新加载cookies，下次请求将使用新的认证信息")

                    self.log(f"❌ 更新失败: {msg}")
                    return False
            else:
                self.log(f"❌ 请求失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            response_time = time.time() - start_time
            self.log(f"❌ 更新异常: {str(e)} (耗时: {response_time:.2f}s)")
            return False
        finally:
            # 更新延时策略
            self._adjust_delay(success, response_time)
    
    def query_product_skus(self, sku_code: str) -> Dict[str, Any]:
        """
        查询商品SKU信息

        注意：当前查询API存在问题，总是返回0个结果
        这是一个临时的模拟实现，用于演示功能

        Args:
            sku_code: 款号，如 "GD340-1011"

        Returns:
            查询结果字典: {
                "success": bool,
                "data": [{"sku_id": "xxx", "cost_price": 123.45, "sale_price": 200.0, ...}],
                "error": str
            }
        """
        self.log(f"🔍 查询商品SKU: {sku_code}")

        # 检查认证状态
        if not self.auth_valid or not self.check_auth_status():
            return {
                "success": False,
                "data": [],
                "error": "认证失效，请更新认证信息"
            }

        # 只使用真实的API查询，不再使用模拟数据
        return self._try_real_query(sku_code)

    def _try_real_query(self, sku_code: str) -> Dict[str, Any]:
        """尝试真实的API查询"""
        # 构建查询URL（使用YUE哥cURL中的格式）
        query_url = f"{self.base_url}/ItemApi/ItemSku/GetPageListV2"
        params = {
            '__from': 'web_component',
            'owner_co_id': self.owner_co_id,
            'authorize_co_id': self.authorize_co_id
        }

        # 查询请求体（使用YUE哥cURL中的格式）
        query_data = {
            "ip": "",
            "uid": "18707109",
            "coid": "13881863",
            "page": {
                "currentPage": 1,
                "pageSize": 50,
                "hasPageInfo": True,
                "pageAction": 1
            },
            "data": {
                "sku_type": 1,
                "has_daily_purchase_in_qty": False,
                "queryFlds": [
                    "shop_sku_num", "pic", "i_id", "sku_id", "name", "properties_value",
                    "sale_price", "cost_price", "supplier_name", "enabled", "modified",
                    "created", "formula_1", "sales_qty_7", "sent_qty_7", "as_qty_7",
                    "sent_qty_15", "as_qty_15", "sent_qty_30", "as_qty_30", "remark",
                    "is_series_number", "c_id", "supplier_id", "supplier_name", "pic_big"
                ],
                "orderBy": "",
                "enabled": "1",
                "i_id": "",
                "sku_id": sku_code,  # 直接搜索SKU编码
                "c_id": ""
            }
        }

        try:
            # 使用requests的json参数，让它自动处理编码
            headers_copy = self.headers.copy()
            headers_copy['content-type'] = 'application/json; charset=utf-8'

            # 设置更合理的超时：5秒连接超时，15秒读取超时
            response = requests.post(
                query_url,
                params=params,
                headers=headers_copy,
                cookies=self.cookies,
                json=query_data,  # 使用json参数，自动处理编码
                timeout=(5, 15)  # (连接超时, 读取超时)
            )

            # 设置正确的编码以处理中文字符
            response.encoding = 'utf-8'

            if response.status_code == 200:
                result = response.json()

                code = result.get('code')
                msg = result.get('msg', '')

                if code == 0 and 'data' in result:
                    skus = result.get('data', [])
                    print(f"✅ 查询成功，找到 {len(skus)} 个SKU")

                    # 处理SKU数据，提取关键信息（使用实际的字段名）
                    processed_skus = []
                    for sku in skus:
                        if isinstance(sku, dict):
                            # 从实际API响应中提取字段
                            sku_id = sku.get("sku_id", "")
                            name = sku.get("name", "")
                            properties_value = sku.get("properties_value", "")
                            cost_price = sku.get("cost_price", 0)
                            sale_price = sku.get("sale_price", 0)

                            # 解析规格信息（格式：颜色;尺码）
                            spec_parts = properties_value.split(';') if properties_value else []
                            color = spec_parts[0] if len(spec_parts) > 0 else ""
                            size = spec_parts[1] if len(spec_parts) > 1 else ""

                            processed_sku = {
                                "sku_id": sku_id,
                                "sku_code": sku_id.split('-')[0] if '-' in sku_id else sku_id,  # 从SKU ID提取款号
                                "cost_price": float(cost_price) if cost_price else 0.0,
                                "sale_price": float(sale_price) if sale_price else 0.0,
                                "item_name": name,
                                "spec": properties_value,
                                "color": color,
                                "size": size,
                                "supplier_name": sku.get("supplier_name", ""),
                                "enabled": sku.get("enabled", ""),
                                # 🖼️ 添加图像字段
                                "pic": sku.get("pic", ""),
                                "pic_big": sku.get("pic_big", ""),
                                # 📊 添加退货率相关字段
                                "sent_qty_7": int(sku.get("sent_qty_7", 0)) if sku.get("sent_qty_7") else 0,
                                "as_qty_7": int(sku.get("as_qty_7", 0)) if sku.get("as_qty_7") else 0,
                                "sent_qty_15": int(sku.get("sent_qty_15", 0)) if sku.get("sent_qty_15") else 0,
                                "as_qty_15": int(sku.get("as_qty_15", 0)) if sku.get("as_qty_15") else 0,
                                "sent_qty_30": int(sku.get("sent_qty_30", 0)) if sku.get("sent_qty_30") else 0,
                                "as_qty_30": int(sku.get("as_qty_30", 0)) if sku.get("as_qty_30") else 0,
                            }
                            processed_skus.append(processed_sku)

                    return {
                        "success": True,
                        "data": processed_skus,
                        "error": ""
                    }
                elif code == -1 and 'token失效' in msg:
                    print(f"❌ Token失效，需要重新登录: {msg}")
                    self.auth_valid = False
                    return {
                        "success": False,
                        "data": [],
                        "error": f"认证失效: {msg}"
                    }
                elif code == 10014:
                    print(f"❌ 无访问权限: {msg}")
                    return {
                        "success": False,
                        "data": [],
                        "error": f"权限不足: {msg}"
                    }
                else:
                    print(f"❌ 查询失败: code={code}, msg={msg}")
                    return {
                        "success": False,
                        "data": [],
                        "error": f"查询失败: {msg}"
                    }
            else:
                return {
                    "success": False,
                    "data": [],
                    "error": f"HTTP {response.status_code}"
                }

        except requests.exceptions.ConnectTimeout:
            self.log(f"❌ 连接超时: {sku_code}")
            return {
                "success": False,
                "data": [],
                "error": "连接超时，请检查网络连接"
            }
        except requests.exceptions.ReadTimeout:
            self.log(f"❌ 读取超时: {sku_code}")
            return {
                "success": False,
                "data": [],
                "error": "读取超时，服务器响应缓慢"
            }
        except requests.exceptions.ConnectionError:
            self.log(f"❌ 连接错误: {sku_code}")
            return {
                "success": False,
                "data": [],
                "error": "网络连接错误，请检查网络状态"
            }
        except requests.exceptions.RequestException as e:
            self.log(f"❌ 请求异常: {sku_code} - {str(e)}")
            return {
                "success": False,
                "data": [],
                "error": f"网络请求异常: {str(e)}"
            }
        except Exception as e:
            self.log(f"❌ 未知异常: {sku_code} - {str(e)}")
            return {
                "success": False,
                "data": [],
                "error": f"查询异常: {str(e)}"
            }



    def update_color_spec_cost_price(self, color_spec_skus: List[Dict[str, Any]],
                                     new_cost_price: float,
                                     progress_callback: Optional[Callable] = None,
                                     use_concurrent: bool = False) -> Dict[str, bool]:
        """
        更新同一颜色规格的所有尺码成本价（支持并发优化）

        Args:
            color_spec_skus: 同一颜色规格的SKU列表
            new_cost_price: 新的成本价
            progress_callback: 进度回调函数
            use_concurrent: 是否使用并发请求（默认False保持兼容性）

        Returns:
            更新结果字典: {"sku_id": success_status, ...}
        """
        if not color_spec_skus:
            return {}

        color = color_spec_skus[0].get("color", "未知颜色")
        self.log(f"🎨 批量更新颜色规格成本价: {color} -> ¥{new_cost_price}")
        self.log(f"📦 涉及 {len(color_spec_skus)} 个尺码: {[sku.get('size', '') for sku in color_spec_skus]}")

        results = {}

        # 🔥 修复：禁用并发模式，避免token失效问题
        # ERP系统不支持同一session的并发访问，会导致token被标记为失效
        if use_concurrent and len(color_spec_skus) > 2:
            self.log(f"⚠️ 检测到并发模式请求，但ERP系统不支持session并发访问")
            self.log(f"🔄 自动切换到优化串行模式，避免token失效")

        # 统一使用串行处理模式（已优化性能）
        results = self._update_sequential(color_spec_skus, new_cost_price, progress_callback, color)

        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)

        self.log(f"📊 颜色规格批量更新完成: {color}")
        self.log(f"📊 成功更新: {success_count}/{total_count} 个尺码")

        return results

    def _update_sequential(self, color_spec_skus: List[Dict[str, Any]],
                          new_cost_price: float,
                          progress_callback: Optional[Callable],
                          color: str) -> Dict[str, bool]:
        """串行更新模式（优化延时策略）"""
        results = {}

        for i, sku in enumerate(color_spec_skus, 1):
            sku_id = sku.get('sku_id')
            size = sku.get('size', '')

            if not sku_id:
                self.log(f"❌ 跳过无效SKU: {sku}")
                results[f"invalid_{i}"] = False
                continue

            # 更新进度
            if progress_callback:
                progress_callback(i, len(color_spec_skus), f"{color}-{size}")

            self.log(f"🔄 更新 {color}-{size}: {sku_id}")

            # 🔥 新增：在每次更新前检查认证状态
            if not self.auth_valid:
                self.log(f"⚠️ 检测到认证失效，尝试重新验证...")
                if not self.check_auth_status():
                    self.log(f"❌ 认证验证失败，跳过当前SKU")
                    results[sku_id] = False
                    continue

            success = self.update_cost_price(sku_id, new_cost_price)
            results[sku_id] = success

            if success:
                self.log(f"✅ {color}-{size} 更新成功")
            else:
                self.log(f"❌ {color}-{size} 更新失败")

            # 🔥 优化：使用智能延时策略
            if i < len(color_spec_skus):
                # 根据成功率动态调整延时
                if success:
                    # 成功时减少延时，提高效率
                    delay = max(0.5, self.current_delay * 0.9)
                else:
                    # 失败时增加延时，避免频率限制
                    delay = min(3.0, self.current_delay * 1.2)

                self.current_delay = delay
                self.log(f"⏱️ 延时 {delay:.2f}s")
                time.sleep(delay)

        return results

    def _update_concurrent(self, color_spec_skus: List[Dict[str, Any]],
                          new_cost_price: float,
                          progress_callback: Optional[Callable],
                          color: str) -> Dict[str, bool]:
        """并发更新模式（性能优化 + token失效检测）"""
        results = {}
        completed_count = 0
        token_failed = False  # token失效标志

        def update_single_sku(sku_data):
            sku, index = sku_data
            sku_id = sku.get('sku_id')
            size = sku.get('size', '')

            if not sku_id:
                return f"invalid_{index}", False, False

            success = self.update_cost_price(sku_id, new_cost_price)
            # 检查是否因为token失效而失败
            token_invalid = not self.auth_valid
            return sku_id, success, token_invalid

        # 使用线程池并发处理
        with ThreadPoolExecutor(max_workers=self.max_concurrent_requests) as executor:
            # 提交所有任务
            future_to_sku = {
                executor.submit(update_single_sku, (sku, i)): (sku, i)
                for i, sku in enumerate(color_spec_skus, 1)
            }

            # 处理完成的任务
            for future in as_completed(future_to_sku):
                sku, i = future_to_sku[future]
                completed_count += 1

                try:
                    sku_id, success, token_invalid = future.result()
                    results[sku_id] = success

                    # 检测token失效
                    if token_invalid:
                        token_failed = True
                        self.log(f"⚠️ 检测到token失效，将回退到串行模式")

                    size = sku.get('size', '')
                    if success:
                        self.log(f"✅ {color}-{size} 更新成功 (并发)")
                    else:
                        self.log(f"❌ {color}-{size} 更新失败 (并发)")

                    # 更新进度
                    if progress_callback:
                        progress_callback(completed_count, len(color_spec_skus), f"{color}-{size}")

                except Exception as e:
                    self.log(f"❌ 并发更新异常: {str(e)}")
                    results[f"error_{i}"] = False

        # 如果检测到token失效，回退到串行模式重试失败的项目
        if token_failed:
            self.log(f"🔄 token失效，回退到串行模式重试失败的项目")
            failed_skus = [sku for sku in color_spec_skus if not results.get(sku.get('sku_id'), True)]
            if failed_skus:
                self.log(f"🔄 重试 {len(failed_skus)} 个失败的尺码")
                # 重新加载cookies并检查认证
                self.load_cookies_from_file()
                if self.check_auth_status():
                    # 使用串行模式重试
                    retry_results = self._update_sequential(failed_skus, new_cost_price, progress_callback, color)
                    results.update(retry_results)

        return results

    def batch_update_cost_prices(self, updates: List[Dict[str, Any]],
                                progress_callback: Optional[Callable] = None) -> Dict[str, bool]:
        """
        批量更新成本价

        Args:
            updates: 更新列表，格式: [{"sku_id": "xxx", "cost_price": 123.45}, ...]
            progress_callback: 进度回调函数

        Returns:
            更新结果字典: {"sku_id": success_status, ...}
        """
        self.log(f"🔄 开始批量更新 {len(updates)} 个商品的成本价")
        results = {}

        for i, update in enumerate(updates, 1):
            sku_id = update.get('sku_id')
            cost_price = update.get('cost_price')

            if not sku_id or cost_price is None:
                self.log(f"❌ 跳过无效数据: {update}")
                results[sku_id or f"invalid_{i}"] = False
                continue

            # 更新进度
            if progress_callback:
                progress_callback(i, len(updates), sku_id)

            success = self.update_cost_price(sku_id, cost_price)
            results[sku_id] = success

            # 添加延时避免频率限制
            if i < len(updates):
                time.sleep(1)

        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        total_count = len(results)

        self.log(f"📊 批量更新完成: 成功 {success_count}/{total_count}")
        return results
    
    def refresh_authentication(self) -> bool:
        """
        刷新认证信息

        Returns:
            bool: 刷新是否成功
        """
        self.log("🔄 尝试刷新认证信息...")

        try:
            # 重新加载cookies文件
            self.load_cookies_from_file()

            # 更新Session的cookies
            self.session.cookies.clear()
            self.session.cookies.update(self.cookies)

            # 检查认证状态
            if self.check_auth_status():
                self.log("✅ 认证刷新成功")
                return True
            else:
                self.log("❌ 认证刷新失败，cookies可能已过期")
                return False

        except Exception as e:
            self.log(f"❌ 认证刷新异常: {str(e)}")
            return False

    def update_cookies(self, new_cookies: Dict[str, str]):
        """
        更新认证cookies

        Args:
            new_cookies: 新的cookies字典
        """
        self.cookies.update(new_cookies)
        # 同时更新Session的cookies
        self.session.cookies.update(new_cookies)
        self.auth_valid = True
        self.last_auth_check = datetime.now()
        self.log(f"✅ 已更新 {len(new_cookies)} 个cookies")
    
    def get_auth_status_info(self) -> Dict[str, Any]:
        """
        获取认证状态信息
        
        Returns:
            认证状态信息字典
        """
        return {
            'auth_valid': self.auth_valid,
            'last_check': self.last_auth_check.strftime("%Y-%m-%d %H:%M:%S") if self.last_auth_check else "未检查",
            'cookies_count': len(self.cookies)
        }

    def get_performance_stats(self) -> Dict[str, Any]:
        """
        获取性能统计信息

        Returns:
            性能统计信息字典
        """
        stats = self.request_stats.copy()
        stats['current_delay'] = self.current_delay
        stats['success_rate'] = (
            stats['successful_requests'] / stats['total_requests'] * 100
            if stats['total_requests'] > 0 else 0
        )
        return stats

    def reset_performance_stats(self):
        """重置性能统计信息"""
        self.request_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0.0
        }
        self.current_delay = 0.5
        self.log("📊 性能统计已重置")

    def update_color_spec_cost_price_optimized(self, color_spec_skus: List[Dict[str, Any]],
                                              new_cost_price: float,
                                              progress_callback: Optional[Callable] = None) -> Dict[str, bool]:
        """
        优化版本的颜色规格成本价更新（自动选择最佳策略 + 智能重试）

        Args:
            color_spec_skus: 同一颜色规格的SKU列表
            new_cost_price: 新的成本价
            progress_callback: 进度回调函数

        Returns:
            更新结果字典: {"sku_id": success_status, ...}
        """
        # 检查是否启用优化
        if not self.optimization_config.get('enabled', True):
            self.log("⚠️ ERP优化已禁用，使用原始方法")
            return self.update_color_spec_cost_price(
                color_spec_skus, new_cost_price, progress_callback, False
            )

        # 🔥 修复：强制禁用并发模式，避免ERP session冲突
        # ERP系统不支持同一session的并发访问，会导致token失效
        use_concurrent = False  # 强制使用串行模式

        strategy_name = "优化串行"
        self.log(f"🚀 使用{strategy_name}模式更新 {len(color_spec_skus)} 个尺码")
        self.log(f"💡 已禁用并发模式以避免ERP token失效问题")

        # 第一次尝试
        results = self.update_color_spec_cost_price(
            color_spec_skus,
            new_cost_price,
            progress_callback,
            use_concurrent
        )

        # 检查是否有失败的更新，且可能是token失效导致的
        failed_count = sum(1 for success in results.values() if not success)
        if failed_count > 0 and not self.auth_valid:
            self.log(f"⚠️ 检测到 {failed_count} 个失败更新，可能是token失效，尝试刷新认证后重试")

            # 尝试刷新认证
            if self.refresh_authentication():
                # 找出失败的SKU
                failed_skus = [
                    sku for sku in color_spec_skus
                    if not results.get(sku.get('sku_id'), True)
                ]

                if failed_skus:
                    self.log(f"🔄 重试 {len(failed_skus)} 个失败的尺码（使用串行模式）")
                    # 使用串行模式重试，更稳定
                    retry_results = self._update_sequential(
                        failed_skus, new_cost_price, progress_callback,
                        color_spec_skus[0].get("color", "未知颜色")
                    )
                    # 更新结果
                    results.update(retry_results)

        return results
    
    def is_configured(self) -> bool:
        """
        检查ERP是否已配置（认证信息是否有效）
        
        Returns:
            bool: 配置是否有效
        """
        # 检查基本配置是否存在
        if not self.cookies or not self.cookies.get('u_id') or not self.cookies.get('u_co_id'):
            self.log("❌ ERP认证信息缺失")
            return False
        
        # 检查认证状态（如果之前没检查过或者认证无效，则重新检查）
        if not self.auth_valid or self.last_auth_check is None:
            return self.check_auth_status()
        
        # 如果距离上次检查超过1小时，重新验证
        if self.last_auth_check:
            time_diff = datetime.now() - self.last_auth_check
            if time_diff.total_seconds() > 3600:  # 1小时
                return self.check_auth_status()
        
        return self.auth_valid

# 使用示例和测试函数
def test_erp_integration():
    """测试ERP集成功能"""
    print("🧪 测试ERP集成功能")
    print("=" * 40)

    # 创建ERP集成器
    erp = ERPIntegration()

    # 检查认证状态
    auth_status = erp.check_auth_status()
    print(f"认证状态: {'✅ 有效' if auth_status else '❌ 无效'}")

    if auth_status:
        # 测试商品查询
        test_sku_code = "GD340-1011"
        query_result = erp.query_product_skus(test_sku_code)
        print(f"商品查询测试: {'✅ 成功' if query_result['success'] else '❌ 失败'}")
        if query_result['success']:
            print(f"找到 {len(query_result['data'])} 个SKU")
            for sku in query_result['data'][:3]:  # 只显示前3个
                print(f"  - {sku['sku_id']}: 成本价 {sku['cost_price']}, 售价 {sku['sale_price']}")

        # 测试单个更新
        test_sku_id = "GD340-9026-衬衫-L"
        test_cost_price = 99.99

        success = erp.update_cost_price(test_sku_id, test_cost_price)
        print(f"单个更新测试: {'✅ 成功' if success else '❌ 失败'}")

    return erp

if __name__ == "__main__":
    test_erp_integration()
