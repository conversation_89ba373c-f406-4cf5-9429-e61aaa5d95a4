"""
主界面控制器 - 负责核心窗口和标签页管理
重构自原始pyqt5_main_gui.py的主窗口部分
"""

import sys
import os
from typing import Optional, List, Dict, Any
from datetime import datetime
import threading
import json

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QSplitter, QGroupBox, QLabel, QPushButton, QTextEdit,
    QTableWidget, QTableWidgetItem, QComboBox, QLineEdit, QSpinBox,
    QCheckBox, QFileDialog, QMessageBox, QProgressBar, QListWidget,
    QScrollArea, QFrame, QGridLayout, QFormLayout, QButtonGroup,
    QHeaderView, QAbstractItemView, QSizePolicy
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSize, QObject
from PyQt5.QtGui import QTextCursor
from PyQt5.QtGui import QFont, QPixmap, QIcon, QPalette, QColor

# 导入现有模块
from modules.config_manager import ConfigManager
from modules.file_manager import FileManager
from modules.ai_processor import AIProcessor
from modules.image_splitter import ImageSplitter, ImageInfo
from modules.supplier_manager import SupplierManager
from modules.erp_integration import ERPIntegration
from modules.sku_matcher import SKUMatcher, MatchStrategy

# 导入UI管理器
from modules.ui_style_manager import UIStyleManager
from modules.ui_constants import UIConstants, StyleHelper
from modules.table_manager import TableManager, TableOperationHelper

# 导入统一的Logger
from modules.utils.logger import ThreadSafeLogger

# 导入新的GUI组件
from .image_manager import ImageManager
from .table_ui_manager import TableUIManager
from .ai_processor_ui import AIProcessorUI
from .config_ui_manager import ConfigUIManager
from .erp_ui_manager import ERPUIManager
from .event_handlers import EventHandlers


class ModernTicketProcessorGUI(QMainWindow):
    """现代化的PyQt5票据处理主界面"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("智能票据处理系统 - PyQt5版本")
        self.setMinimumSize(UIConstants.WINDOW_MIN_WIDTH, UIConstants.WINDOW_MIN_HEIGHT)

        # 先不显示窗口，等界面完全初始化后再显示

        # 设置全局异常处理
        import sys
        sys.excepthook = self.handle_exception

        # 初始化核心组件
        self.config_manager = ConfigManager()
        self.file_manager = FileManager()
        self.ai_processor = AIProcessor()
        self.image_splitter = ImageSplitter()
        self.supplier_manager = SupplierManager()
        self.erp_integration = ERPIntegration(log_callback=self.log_message)
        self.sku_matcher = SKUMatcher()

        # 界面状态变量
        self.processing = False
        self.current_image_info: Optional[ImageInfo] = None
        self.current_results = []

        # 线程管理
        self.erp_query_thread = None
        self.cost_update_thread = None
        self.ai_thread = None

        # ERP匹配结果缓存
        self.erp_match_results = {}  # {row: MatchResult}

        # 图像重命名状态持久化
        self.image_rename_cache = {}  # 存储重命名状态 {file_path: {display_name, supplier_code, is_renamed, original_name}}

        # 表格状态常驻存储
        self.table_persistent_data = {
            'pickup': {},  # 存储每个文件的取货
            'return': {}   # 存储每个文件的退货数据 {file_path: [items]}
        }
        self.current_selected_file = None  # 当前选中的文件

        # 颜色确认状态常驻存储
        self.color_confirm_states = {}  # 存储每个文件的确认状态 {file_path: {row: confirm_data}}

        # 表格管理器（将在各UI管理器中初始化）
        self.pickup_table_manager = None
        self.return_table_manager = None

        # 初始化界面状态变量
        self.log_text = None  # 将在setup_ui中初始化

        # 初始化线程安全的日志系统
        self.logger = ThreadSafeLogger(callback=self.log_message_to_ui)

        # 初始化GUI组件管理器
        self.image_manager = ImageManager(self)
        self.table_ui_manager = TableUIManager(self)
        self.ai_processor_ui = AIProcessorUI(self)
        self.config_ui_manager = ConfigUIManager(self)
        self.erp_ui_manager = ERPUIManager(self)
        self.event_handlers = EventHandlers(self)

        # 优化初始化顺序，减少白屏时间
        # 1. 先创建界面结构
        self.setup_ui()

        # 2. 然后设置主题（避免样式加载延迟）
        self.setup_dark_theme()

        # 3. 强制处理所有待处理的事件，确保界面完全渲染
        QApplication.processEvents()

        # 4. 现在可以安全地显示窗口（全屏模式）
        self.showMaximized()

        # 5. 延迟加载配置和数据，减少延迟时间
        QTimer.singleShot(50, self.delayed_initialization)

        # 启动时清理临时文件
        self.cleanup_temp_segments()

        # 设置关闭事件
        self.closeEvent = self.on_closing

        # 初始化AI处理器
        self.ai_processor = AIProcessor()
        
        # 初始化属性
        self.current_image_path = None
        
        # 工作状态标记
        self.processing_files = False
        
        # 窗口状态
        self.is_maximized = False
        
        # 🔥 新增：双向选择同步标志位，防止无限循环
        self._syncing_selection = False

    def delayed_initialization(self):
        """延迟初始化，避免启动时UI阻塞"""
        try:
            # 优化加载流程，减少启动时间
            # 显示加载状态
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage("正在加载配置...")

            # 加载配置（在界面创建完成后）
            self.load_configuration()

            # 刷新当前cookie数据（异步执行，不阻塞UI）
            QTimer.singleShot(100, self.refresh_current_cookie_data)

            # 更新状态
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage("就绪")

            # 记录启动完成
            self.log_message("🚀 系统启动完成", "SUCCESS")

        except Exception as e:
            self.log_message(f"延迟初始化失败: {str(e)}", "ERROR")
            if hasattr(self, 'status_bar'):
                self.status_bar.showMessage("初始化失败")

    def setup_dark_theme(self):
        """设置黑色主题"""
        # 立即应用主题，避免白屏
        UIStyleManager.apply_dark_theme(self)

        # 强制刷新样式
        self.style().unpolish(self)
        self.style().polish(self)

        # 确保所有子组件也应用样式
        for child in self.findChildren(QWidget):
            child.style().unpolish(child)
            child.style().polish(child)

        # 立即更新显示
        self.update()

    def setup_ui(self):
        """设置用户界面"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建标签页组件
        self.tab_widget = QTabWidget()
        self.tab_widget.currentChanged.connect(self._on_tab_changed)

        # 删除强制字体设置，使用CSS样式控制
        # 设置标签页高度和样式
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #3a3a3a;
                background-color: #161617;
                border-radius: 8px;
                top: -1px;
            }
            QTabBar::tab {
                background-color: #161617;
                color: #ffffff;
                padding: 6px 14px;
                margin-right: 1px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: 500;
                font-size: 11px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                min-height: 20px;
                max-height: 20px;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background-color: #0071e3;
                color: white;
            }
            QTabBar::tab:hover:!selected {
                background-color: #3a3a3a;
            }
        """)

        main_layout.addWidget(self.tab_widget)

        # 创建各个标签页
        self.create_main_tab()
        self.create_ai_response_tab()
        self.create_log_tab()
        self.config_ui_manager.create_config_tab()

        # 设置状态栏
        self.setup_status_bar()

    def create_status_bar_progress(self):
        """在状态栏中创建进度条"""
        # 🔥 新增：创建进度条容器，放置在状态栏中
        self.progress_container = QWidget()
        self.progress_container.setVisible(False)  # 初始隐藏

        # 进度条布局
        progress_layout = QHBoxLayout(self.progress_container)
        progress_layout.setContentsMargins(5, 0, 5, 0)
        progress_layout.setSpacing(8)

        # 进度描述标签
        self.progress_description = QLabel("准备中...")
        self.progress_description.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 10px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        self.progress_description.setFixedWidth(120)
        progress_layout.addWidget(self.progress_description)

        # 🔥 修改：调整进度条尺寸为高度10px，长度500px
        self.unified_progress_bar = QProgressBar()
        self.unified_progress_bar.setFixedHeight(10)
        self.unified_progress_bar.setFixedWidth(500)
        self.unified_progress_bar.setRange(0, 100)
        self.unified_progress_bar.setValue(0)
        self.unified_progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #3a3a3a;
                border-radius: 5px;
                background-color: #161617;
                text-align: center;
                color: #ffffff;
                font-size: 9px;
                font-weight: bold;
            }
            QProgressBar::chunk {
                background-color: #9c27b0;
                border-radius: 4px;
            }
        """)
        progress_layout.addWidget(self.unified_progress_bar)

        # 🔥 新增：将进度条容器添加到状态栏
        self.status_bar.addPermanentWidget(self.progress_container)

    def create_main_tab(self):
        """创建主标签页"""
        main_tab = QWidget()
        self.tab_widget.addTab(main_tab, f"{UIConstants.Icons.MAIN} 主操作")

        main_layout = QVBoxLayout(main_tab)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)

        # 创建主分割器
        main_splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(main_splitter)

        # 左侧：图像预览区域
        self.image_manager.create_left_image_panel(main_splitter)

        # 中间：商品明细表格区域
        self.table_ui_manager.create_center_table_panel(main_splitter)

        # 右侧：人工待确认区域
        self.create_right_confirm_panel(main_splitter)

        # 设置分割器初始宽度比例
        main_splitter.setSizes(UIConstants.MAIN_SPLITTER_SIZES)

        # 🔥 新增：设置左侧面板固定宽度为700px
        # 获取左侧面板（第0个widget）并设置固定宽度
        if main_splitter.count() > 0:
            left_widget = main_splitter.widget(0)
            if left_widget:
                left_widget.setFixedWidth(700)
                # 设置分割器的拉伸因子，让中间和右侧面板自动调整
                main_splitter.setStretchFactor(0, 0)  # 左侧不拉伸（固定宽度）
                main_splitter.setStretchFactor(1, 0)  # 🔥 修改：中间表格区域也固定宽度
                main_splitter.setStretchFactor(2, 1)  # 🔥 修改：右侧面板可拉伸

    def create_right_confirm_panel(self, parent):
        """创建右侧人工待确认区域"""
        right_widget = QWidget()
        parent.addWidget(right_widget)

        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(5, 5, 5, 5)
        right_layout.setSpacing(0)  # 保持紧凑间距

        # 上半部分：解析结果表格（固定高度400px）
        self.create_result_table_section(right_layout)

        # 下半部分：颜色确认面板（占用剩余空间）
        self.create_color_confirm_section(right_layout)

    def create_result_table_section(self, parent):
        """创建解析结果表格区域（固定高度400px）"""
        result_widget = QWidget()
        result_widget.setFixedHeight(400)  # 固定高度400px
        parent.addWidget(result_widget)

        result_layout = QVBoxLayout(result_widget)
        result_layout.setContentsMargins(5, 5, 5, 5)
        result_layout.setSpacing(8)

        # 解析列表标题和按钮
        result_header_layout = QHBoxLayout()

        result_label = QLabel("解析列表")
        result_label.setFont(QFont("Arial", 10, QFont.Bold))
        result_label.setStyleSheet("color: #ffffff;")
        result_header_layout.addWidget(result_label)

        result_header_layout.addStretch()

        # 合并表格按钮
        self.merge_tables_btn = QPushButton("🔗 合并表格")
        self.merge_tables_btn.clicked.connect(self.table_ui_manager.merge_split_results)
        self.merge_tables_btn.setMinimumHeight(25)
        self.merge_tables_btn.setMinimumWidth(80)
        self.merge_tables_btn.setEnabled(False)  # 默认禁用，有分割结果时启用
        result_header_layout.addWidget(self.merge_tables_btn)

        # 撤销合并按钮
        self.undo_merge_btn = QPushButton("↩️ 撤销合并")
        self.undo_merge_btn.clicked.connect(self.table_ui_manager.undo_merge_results)
        self.undo_merge_btn.setMinimumHeight(25)
        self.undo_merge_btn.setMinimumWidth(80)
        self.undo_merge_btn.setEnabled(False)  # 默认禁用，有合并结果时启用
        result_header_layout.addWidget(self.undo_merge_btn)

        result_layout.addLayout(result_header_layout)

        # 创建滚动区域包装表格
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #161617;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #666666;
            }
        """)

        # 创建解析结果表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(8)
        self.result_table.setHorizontalHeaderLabels([
            "序号", "文件名", "供应商", "数量总和", "小计总和", "类型", "日期", "操作"
        ])

        # 设置表格属性
        self.result_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.result_table.setAlternatingRowColors(True)
        self.result_table.horizontalHeader().setStretchLastSection(True)
        self.result_table.verticalHeader().setVisible(False)
        
        # 🔥 设置行高为45px
        self.result_table.verticalHeader().setDefaultSectionSize(45)

        # 设置表格样式
        self.result_table.setStyleSheet("""
            QTableWidget {
                background-color: #161617;
                color: white;
                gridline-color: #555555;
                selection-background-color: #0071e3;
                border: 1px solid #3a3a3a;
                border-radius: 6px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 12px;
            }
            QTableWidget::item {
                background-color: #161617;
                /* 删除强制的color设置，让setForeground()生效 */
                border: none;
                border-right: 1px solid #555555;
                border-bottom: 1px solid #555555;
                padding: 4px;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 12px;
                text-align: center;
                outline: none;
            }
            QTableWidget::item:selected {
                background-color: #0071e3;
                color: white;
            }
            QTableWidget::item:focus {
                background-color: #1a1a1b;
                color: white;
                border: 1px solid #0071e3;
            }
            QHeaderView::section {
                background-color: #161617;
                color: white;
                padding: 8px;
                border: 1px solid #555;
                font-weight: bold;
                font-family: 'Microsoft YaHei', 'SimHei', sans-serif;
                font-size: 12px;
            }
        """)

        # 设置列宽 - 删除状态列，调整列索引
        header = self.result_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.Fixed)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.setSectionResizeMode(4, QHeaderView.Fixed)
        header.setSectionResizeMode(5, QHeaderView.Fixed)
        header.setSectionResizeMode(6, QHeaderView.Fixed)
        header.setSectionResizeMode(7, QHeaderView.Fixed)

        self.result_table.setColumnWidth(0, 50)
        self.result_table.setColumnWidth(2, 80)
        self.result_table.setColumnWidth(3, 60)
        self.result_table.setColumnWidth(4, 60)
        self.result_table.setColumnWidth(5, 60)
        self.result_table.setColumnWidth(6, 80)
        self.result_table.setColumnWidth(7, 80)

        # 连接选择事件
        self.result_table.itemSelectionChanged.connect(self.table_ui_manager.on_result_selection_changed)

        # 将表格添加到滚动区域
        scroll_area.setWidget(self.result_table)
        result_layout.addWidget(scroll_area)

        # 将表格引用传递给table_ui_manager
        self.table_ui_manager.result_table = self.result_table

    def create_color_confirm_section(self, parent):
        """创建颜色确认面板区域"""
        confirm_widget = QWidget()
        parent.addWidget(confirm_widget)

        confirm_layout = QVBoxLayout(confirm_widget)
        confirm_layout.setContentsMargins(5, 0, 5, 5)  # 减少上边距
        confirm_layout.setSpacing(2)  # 减少间距

        # 人工确认面板
        self.create_color_confirm_panel(confirm_layout)

    def create_color_confirm_panel(self, layout):
        """创建颜色确认面板"""
        from modules.color_confirm_panel import ColorConfirmPanel

        # 创建确认面板，传递主窗口作为父窗口
        self.color_confirm_panel = ColorConfirmPanel(self)
        self.color_confirm_panel.color_confirmed.connect(self.on_color_confirmed)
        layout.addWidget(self.color_confirm_panel)

        # 面板始终显示，不设置高度限制（由面板自己控制）

    def on_color_confirmed(self, row, selected_color_groups, selected_skus):
        """颜色规格确认回调（智能价格比较）"""
        try:

            # 获取票据价格 - 支持多个表格
            table = getattr(self, 'current_query_table', None)
            if table is None:
                # 尝试获取当前活动的表格
                if hasattr(self.table_ui_manager, 'table_tab_widget'):
                    current_tab_index = self.table_ui_manager.table_tab_widget.currentIndex()
                    if current_tab_index == 0:
                        table = self.table_ui_manager.pickup_table
                    elif current_tab_index == 1:
                        table = self.table_ui_manager.return_table
                    else:
                        table = self.table_ui_manager.pickup_table  # 默认使用pickup表格
                else:
                    table = self.table_ui_manager.pickup_table
            price_item = table.item(row, 4)  # 单价列
            if not price_item:
                self.log_message(f"第{row+1}行无法获取票据价格", "ERROR")
                return

            try:
                ticket_price = float(price_item.text())
            except (ValueError, TypeError):
                self.log_message(f"第{row+1}行票据价格格式错误", "ERROR")
                return

            # 使用传递的selected_skus（已经是整个商品链接的SKU数据）
            # 但是需要从ERP匹配结果中获取正确的成本价信息
            current_row_skus = selected_skus  # 直接使用传递的商品链接级SKU数据
            erp_cost_price = None

            # 从ERP匹配结果中获取成本价信息（用于价格比较）
            if hasattr(self, 'erp_match_results') and row in self.erp_match_results:
                erp_data = self.erp_match_results[row]
                color_groups = erp_data.get('color_groups', {})

                # 获取选中颜色组的成本价（用于价格比较显示）
                for color_name in selected_color_groups.keys():
                    if color_name in color_groups and color_groups[color_name]:
                        erp_cost_price = color_groups[color_name][0].get("cost_price", None)
                        break  # 只需要获取一个成本价用于比较

            # 如果没有获取到成本价，从传递的SKU数据中获取
            if erp_cost_price is None and current_row_skus:
                erp_cost_price = current_row_skus[0].get("cost_price", None)

            # 智能价格比较（与重构前逻辑一致）
            if current_row_skus and erp_cost_price is not None:
                if erp_cost_price == "" or erp_cost_price == 0:
                    # ERP中没有成本价，这是首次设置
                    status = "✅ 新增"
                else:
                    try:
                        erp_price_float = float(erp_cost_price)
                        difference = ticket_price - erp_price_float

                        if abs(difference) < 0.01:
                            # 价格相同
                            status = "✅ 无变化"
                        elif difference > 0.01:
                            # 价格上涨 - 🔥 修改：删除文字描述，只保留emoji和数值
                            status = f"📈 ¥{difference:.0f}"
                        else:
                            # 价格下降 - 🔥 修改：删除文字描述，只保留emoji和数值
                            status = f"📉 ¥{abs(difference):.0f}"
                    except (ValueError, TypeError):
                        status = "✅ 新增"  # 价格转换失败，默认显示新增
            else:
                status = "✅ 已确认"

            # 更新匹配结果缓存
            if hasattr(self, 'erp_match_results') and row in self.erp_match_results:
                self.erp_match_results[row]["confirmed"] = True
                self.erp_match_results[row]["selected_color_groups"] = selected_color_groups
                self.erp_match_results[row]["selected_skus"] = current_row_skus
                self.erp_match_results[row]["status"] = status

            # 更新表格状态显示
            self.set_combined_status(table, row, match_status=status)

            # 计算并更新利润列，使用选中颜色规格的售价
            self._calculate_profit_for_confirmed_item(table, row, current_row_skus, ticket_price, selected_color_groups)

            # 📊 计算并更新退货率列
            self._calculate_return_rate_for_confirmed_item(table, row, current_row_skus)

            # 构建颜色信息用于日志
            color_names = list(selected_color_groups.keys())
            if len(color_names) == 1:
                color_info = color_names[0]
            else:
                color_info = f"{len(color_names)}个颜色规格"

            self.log_message(f"第{row+1}行已确认颜色规格: {color_info}，状态: {status}")

        except Exception as e:
            self.log_message(f"确认颜色规格时出错: {str(e)}", "ERROR")

    def _calculate_profit_for_confirmed_item(self, table, row, selected_skus, ticket_price, selected_color_groups=None):
        """计算确认商品的利润"""
        try:
            if not selected_skus:
                return

            # 获取用户选择的颜色规格的售价
            sale_price = 0
            if selected_color_groups:
                # 从ERP匹配结果中获取选中颜色规格的售价
                if hasattr(self, 'erp_match_results') and row in self.erp_match_results:
                    erp_data = self.erp_match_results[row]
                    color_groups = erp_data.get('color_groups', {})

                    # 获取用户选择的第一个颜色规格的售价
                    for color_name in selected_color_groups.keys():
                        if color_name in color_groups and color_groups[color_name]:
                            sale_price = color_groups[color_name][0].get('sale_price', 0)
                            break

            # 如果没有获取到售价，回退到第一个SKU的售价
            if sale_price == 0 and selected_skus:
                sale_price = selected_skus[0].get('sale_price', 0)

            try:
                sale_price_float = float(sale_price)
                cost_price_float = float(ticket_price)

                # 正确的利润计算公式 = 售价 - 100（固定扣除）- 表格成本价
                profit = sale_price_float - 100 - cost_price_float

                # 简化显示格式：只显示利润金额
                profit_text = f"¥{profit:.0f}"

                # 设置利润列（第7列）
                profit_item = QTableWidgetItem(profit_text)
                profit_item.setTextAlignment(Qt.AlignCenter)

                # 根据利润设置颜色
                if profit < 20:
                    profit_item.setForeground(QColor(244, 67, 54))  # 红色
                else:
                    profit_item.setForeground(QColor(76, 175, 80))  # 绿色

                table.setItem(row, 7, profit_item)
                print(f"第{row+1}行：确认后利润计算完成 = {profit_text} (售价{sale_price_float} - 成本{cost_price_float})")

                # 将利润数据保存到erp_match_results中
                if hasattr(self, 'erp_match_results') and row in self.erp_match_results:
                    self.erp_match_results[row]["profit_text"] = profit_text
                    self.erp_match_results[row]["profit_value"] = profit
                    self.erp_match_results[row]["sale_price"] = sale_price_float
                    self.erp_match_results[row]["cost_price"] = cost_price_float
                    print(f"💾 第{row+1}行：利润数据已保存到ERP状态")

            except (ValueError, TypeError) as e:
                print(f"第{row+1}行：确认后价格转换失败: {e}")

        except Exception as e:
            print(f"确认商品利润计算失败: {str(e)}")

    def _calculate_return_rate_for_confirmed_item(self, table, row, selected_skus):
        """计算确认商品的退货率（商品链接级别，两列格式）"""
        try:
            if not selected_skus:
                print(f"❌ 第{row+1}行：没有选中的SKU数据，无法计算退货率")
                return

            # 使用正确的table_manager访问方式
            current_table_type = self.table_ui_manager.get_current_table_type()
            if current_table_type == "pickup":
                table_manager = self.pickup_table_manager
            else:
                table_manager = self.return_table_manager

            if table_manager:
                table_manager.set_return_rate_by_product_links(row, selected_skus)
            else:
                # 备用方案：直接计算并更新表格退货率列（两列格式）
                from modules.return_rate_calculator import ReturnRateCalculator
                calculator = ReturnRateCalculator()
                link_rates = calculator.calculate_return_rates_by_product_links(selected_skus)
                print(f"🔗 第{row+1}行：商品链接识别结果: {len(link_rates)} 个链接")
                for link_id, rates in link_rates.items():
                    print(f"   - {link_id}: 15天={rates.get('15天', 'None')}%, 30天={rates.get('30天', 'None')}%, sent_qty_30={rates.get('sent_qty_30', 0)}")

                formatted_rates = calculator.format_multi_link_display(link_rates)

                # 🔥 新增：同时更新30天实发列
                sent_qty_text = calculator.format_multi_link_sent_qty_30_display(link_rates)
                print(f"📊 第{row+1}行：30天实发格式化结果 = {sent_qty_text}")

                # 更新表格退货率两列：15退(第8列)、30退(第9列)
                if row < table.rowCount():
                    from PyQt5.QtWidgets import QTableWidgetItem
                    from PyQt5.QtCore import Qt
                    from PyQt5.QtGui import QColor

                    periods = ["15天", "30天"]  # 🔥 删除7天
                    for i, period in enumerate(periods):
                        col_index = 8 + i  # 第8、9列
                        rate_text = formatted_rates[period]

                        return_rate_item = QTableWidgetItem(rate_text)
                        return_rate_item.setTextAlignment(Qt.AlignCenter)
                        return_rate_item.setFlags(return_rate_item.flags() & ~Qt.ItemIsEditable)

                        # 设置颜色（只对单链接设置颜色）
                        if rate_text != "-" and "|" not in rate_text:
                            try:
                                rate_value = float(rate_text.replace("%", ""))
                                if rate_value < 20:
                                    color = "#4CAF50"  # 绿色
                                elif rate_value < 50:
                                    color = "#FF9800"  # 橙色
                                else:
                                    color = "#F44336"  # 红色
                                return_rate_item.setForeground(QColor(color))
                            except:
                                pass

                        table.setItem(row, col_index, return_rate_item)

                    # 🔥 新增：更新30天实发列（第10列）
                    sent_item = QTableWidgetItem(sent_qty_text)
                    sent_item.setTextAlignment(Qt.AlignCenter)
                    sent_item.setFlags(sent_item.flags() & ~Qt.ItemIsEditable)
                    sent_item.setForeground(QColor(255, 255, 255))  # 白色字体
                    table.setItem(row, 10, sent_item)

                    print(f"📊 第{row+1}行：商品链接退货率和30天实发计算完成 - 15退:{formatted_rates['15天']} 30退:{formatted_rates['30天']} 30天实发:{sent_qty_text}")

                # 保存退货率和30天实发数据到erp_match_results中
                if hasattr(self, 'erp_match_results') and row in self.erp_match_results:
                    self.erp_match_results[row]["link_rates"] = link_rates
                    self.erp_match_results[row]["formatted_rates"] = formatted_rates
                    self.erp_match_results[row]["sent_qty_30_text"] = sent_qty_text
                    print(f"💾 第{row+1}行：商品链接退货率和30天实发数据已保存到ERP状态")

        except Exception as e:
            print(f"确认商品退货率计算失败: {str(e)}")

    def set_combined_status(self, table, row, erp_status=None, match_status=None):
        """设置组合状态（兼容性方法）"""
        try:
            # 简化的状态设置逻辑
            if match_status:
                status_text = match_status
            elif erp_status:
                status_text = erp_status
            else:
                status_text = "⏳ 待处理"

            # 设置状态列（第6列）
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter)
            table.setItem(row, 6, status_item)

            # 🔥 修改：根据状态设置字体颜色而非背景色
            if "📈" in status_text:
                status_item.setForeground(QColor("#ff3e66"))  # 统一红色字体（价格上涨）
            elif "📉" in status_text:
                status_item.setForeground(QColor(76, 175, 80))  # 绿色字体（价格下降）
            elif "✅" in status_text:
                status_item.setForeground(QColor(76, 175, 80))  # 绿色字体（更新成功）
            elif "🟡待确认" in status_text or "待确认" in status_text:
                status_item.setForeground(QColor(255, 152, 0))  # 橙色字体（待确认）
            elif "❌" in status_text or "⚠️" in status_text:
                status_item.setForeground(QColor("#ff3e66"))  # 统一红色字体（错误）
            else:
                status_item.setForeground(QColor(255, 255, 255))  # 白色字体（默认）

        except Exception as e:
            print(f"设置状态失败: {str(e)}")

    def save_color_confirm_state(self, row, confirm_data):
        """保存颜色确认状态（兼容性方法）"""
        try:
            if self.current_selected_file:
                if self.current_selected_file not in self.color_confirm_states:
                    self.color_confirm_states[self.current_selected_file] = {}
                self.color_confirm_states[self.current_selected_file][row] = confirm_data
                print(f"✅ 保存确认状态: 文件{self.current_selected_file}, 行{row+1}")
        except Exception as e:
            print(f"❌ 保存确认状态失败: {str(e)}")

    def restore_color_confirm_state(self, file_path, row):
        """恢复颜色确认状态（兼容性方法）"""
        try:
            if file_path in self.color_confirm_states and row in self.color_confirm_states[file_path]:
                confirm_data = self.color_confirm_states[file_path][row]
                if hasattr(self, 'color_confirm_panel'):
                    self.color_confirm_panel.restore_confirmation_state(confirm_data)
                    print(f"✅ 恢复确认状态: 文件{file_path}, 行{row+1}")
        except Exception as e:
            print(f"❌ 恢复确认状态失败: {str(e)}")

    def create_ai_response_tab(self):
        """创建AI响应标签页"""
        self.ai_processor_ui.create_ai_response_tab()

    def create_log_tab(self):
        """创建日志标签页"""
        log_tab = QWidget()
        self.tab_widget.addTab(log_tab, f"{UIConstants.Icons.LOG} 系统日志")

        log_layout = QVBoxLayout(log_tab)
        log_layout.setContentsMargins(5, 5, 5, 5)

        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1e1e1e;
                color: #ffffff;
                border: 1px solid #444444;
            }
        """)
        log_layout.addWidget(self.log_text)

        # 日志操作按钮
        log_button_layout = QHBoxLayout()

        self.clear_log_btn = QPushButton(f"{UIConstants.Icons.CLEAR} 清空日志")
        self.clear_log_btn.clicked.connect(self.clear_log)
        log_button_layout.addWidget(self.clear_log_btn)

        self.export_log_btn = QPushButton(f"{UIConstants.Icons.EXPORT} 导出日志")
        self.export_log_btn.clicked.connect(self.export_log)
        log_button_layout.addWidget(self.export_log_btn)

        log_button_layout.addStretch()
        log_layout.addLayout(log_button_layout)

        # 初始化日志行数计数器
        self._log_line_count = 0
        self._max_log_lines = 1000

    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")

        # 🔥 新增：在状态栏中添加进度条
        self.create_status_bar_progress()

        # 添加永久状态指示器
        self.connection_status = QLabel("🔴 未连接")
        self.status_bar.addPermanentWidget(self.connection_status)

    def show_unified_progress(self, description="处理中..."):
        """显示统一进度条"""
        if hasattr(self, 'progress_container'):
            self.progress_container.setVisible(True)
            self.unified_progress_bar.setValue(0)
            self.progress_description.setText(description)
            # 🔥 新增：隐藏状态栏默认消息，显示进度条
            self.status_bar.clearMessage()

    def update_unified_progress(self, value, description=None):
        """更新统一进度条"""
        if hasattr(self, 'unified_progress_bar'):
            self.unified_progress_bar.setValue(value)
            if description:
                self.progress_description.setText(description)

    def hide_unified_progress(self):
        """隐藏统一进度条"""
        if hasattr(self, 'progress_container'):
            self.progress_container.setVisible(False)
            # 🔥 新增：恢复状态栏默认消息
            self.status_bar.showMessage("就绪")

    def _on_tab_changed(self, index):
        """标签页切换事件处理（性能优化）"""
        try:
            # 获取当前标签页的标题
            tab_text = self.tab_widget.tabText(index)

            # 如果切换到系统日志标签页，确保日志文本区域已正确初始化
            if "系统日志" in tab_text and hasattr(self, 'log_text'):
                # 强制刷新日志显示
                QTimer.singleShot(50, self._refresh_log_display)
        except Exception as e:
            # 静默处理标签页切换错误
            pass

    def _refresh_log_display(self):
        """刷新日志显示"""
        if hasattr(self, 'log_text') and self.log_text is not None:
            try:
                # 滚动到底部
                cursor = self.log_text.textCursor()
                cursor.movePosition(QTextCursor.End)
                self.log_text.setTextCursor(cursor)
            except Exception:
                pass

    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """全局异常处理"""
        import traceback
        error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
        
        # 记录到日志
        self.log_message(f"💥 系统异常: {str(exc_value)}", "ERROR")
        
        # 显示用户友好的错误消息
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage(f"❌ 系统错误: {str(exc_value)}")
        
        # 在开发模式下显示详细错误
        print("=== 系统异常 ===")
        print(error_msg)
        print("================")

    # 日志相关方法
    def log_message_to_ui(self, message: str):
        """日志消息UI回调（线程安全，带日志数量限制）"""
        if hasattr(self, 'log_text') and self.log_text is not None:
            try:
                # 检查日志行数，如果超过限制则清理
                if hasattr(self, '_log_line_count'):
                    self._log_line_count += 1
                    if self._log_line_count > self._max_log_lines:
                        # 清空日志并重置计数器
                        self.log_text.clear()
                        self._log_line_count = 1
                        self.log_text.append("=== 日志已自动清理 ===")

                # 添加新日志
                self.log_text.append(message)

                # 自动滚动到底部（使用QTimer避免频繁滚动）
                if not hasattr(self, '_scroll_timer'):
                    self._scroll_timer = QTimer()
                    self._scroll_timer.setSingleShot(True)
                    self._scroll_timer.timeout.connect(self._scroll_to_bottom)

                self._scroll_timer.start(100)  # 100ms后滚动
            except Exception:
                # 静默处理日志添加错误
                pass
        else:
            # 如果log_text还未初始化，忽略日志
            pass

    def _scroll_to_bottom(self):
        """滚动日志到底部"""
        if hasattr(self, 'log_text') and self.log_text is not None:
            try:
                cursor = self.log_text.textCursor()
                cursor.movePosition(QTextCursor.End)
                self.log_text.setTextCursor(cursor)
            except Exception:
                pass

    def log_message(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        # 输出到控制台
        print(log_entry)
        
        # 使用线程安全的日志系统
        if hasattr(self, 'logger'):
            self.logger.log(message, level)

    def clear_log(self):
        """清空日志"""
        if hasattr(self, 'log_text') and self.log_text is not None:
            self.log_text.clear()
            self._log_line_count = 0

    def export_log(self):
        """导出日志"""
        if hasattr(self, 'log_text') and self.log_text is not None:
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"system_log_{timestamp}.txt"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                
                self.log_message(f"日志已导出到: {filename}")
                QMessageBox.information(self, "导出成功", f"日志已导出到: {filename}")
            except Exception as e:
                self.log_message(f"导出日志失败: {str(e)}", "ERROR")

    def load_configuration(self):
        """加载配置"""
        self.config_ui_manager.load_configuration()

    # 文件操作方法委托给event_handlers
    def select_files(self):
        """选择文件"""
        self.event_handlers.select_files()

    def paste_image(self):
        """粘贴图像"""
        self.event_handlers.paste_image()

    def clear_files(self):
        """清除文件"""
        self.event_handlers.on_file_cleared()

    def split_selected_image(self):
        """分割选定图像"""
        self.event_handlers.split_selected_image()

    # AI处理方法委托给ai_processor_ui
    def start_ai_processing(self):
        """开始AI处理"""
        self.ai_processor_ui.start_ai_processing()

    def start_ai_processing_selected(self):
        """开始AI处理选定图像"""
        self.ai_processor_ui.start_ai_processing_selected()

    def copy_ai_response(self):
        """复制AI响应"""
        self.ai_processor_ui.copy_ai_response()

    def clear_ai_response(self):
        """清空AI响应"""
        self.ai_processor_ui.clear_ai_response()

    def on_closing(self, event):
        """关闭事件处理"""
        try:
            # 停止所有线程
            if self.ai_thread and self.ai_thread.isRunning():
                self.ai_thread.requestInterruption()
                self.ai_thread.wait(3000)  # 等待3秒
                
            if self.erp_query_thread and self.erp_query_thread.isRunning():
                self.erp_query_thread.requestInterruption()
                self.erp_query_thread.wait(3000)  # 等待3秒
            
            if self.cost_update_thread and self.cost_update_thread.isRunning():
                self.cost_update_thread.requestInterruption()
                self.cost_update_thread.wait(3000)
            
            # 保存配置
            self.config_ui_manager.save_configuration()

            # 清理临时分割文件
            self.cleanup_temp_segments()

            # 接受关闭事件
            event.accept()
            
        except Exception as e:
            print(f"关闭时发生错误: {str(e)}")
            event.accept()  # 即使出错也要关闭

    def cleanup_temp_segments(self):
        """清理临时分割文件夹"""
        try:
            import shutil
            temp_dir = "temp_segments"

            if os.path.exists(temp_dir):
                # 删除文件夹及其所有内容
                shutil.rmtree(temp_dir)
                self.log_message("✅ 已清理临时分割文件", "SUCCESS")

        except Exception as e:
            self.log_message(f"清理临时文件失败: {str(e)}", "ERROR")

    # ==================== 简化的Cookies处理方法 ====================
    # 参考另一个程序的实现方式

    def refresh_current_cookie_data(self):
        """刷新当前cookie数据（优化版本，快速加载）"""
        try:
            cookie_file = "latest_cookies.json"
            if os.path.exists(cookie_file):
                # 快速检查文件大小，避免读取过大文件
                file_size = os.path.getsize(cookie_file)
                if file_size > 1024 * 1024:  # 1MB限制
                    self.log_message(f"⚠️ Cookie文件过大 ({file_size} bytes)，跳过加载")
                    return

                with open(cookie_file, 'r', encoding='utf-8') as f:
                    current_cookies = json.load(f)
                self.log_message(f"✅ 已加载 {len(current_cookies)} 个cookies")
            else:
                self.log_message(f"❌ 未找到Cookie文件")
        except FileNotFoundError:
            self.log_message(f"❌ 未找到Cookie文件")
        except Exception as e:
            self.log_message(f"❌ Cookie文件读取异常: {str(e)}")

    def parse_cookie_text(self, cookie_text):
        """解析cookie文本（参考另一个程序的方式）"""
        cookies = {}
        lines = cookie_text.split('\n')

        for line in lines:
            # 格式1: name=value
            if '=' in line and not line.startswith('Set-Cookie:'):
                parts = line.split('=', 1)
                cookies[parts[0].strip()] = parts[1].strip()
            # 格式2: 可能是其他格式，可以扩展
            elif '\t' in line:
                parts = line.split('\t')
                cookies[parts[0].strip()] = parts[1].strip()

        # 过滤重要的Cookie
        important_keys = [
            'u_sso_token', '_sid18707109', '3AB9D23F7A4B3CSS',
            'acw_tc', 'u_json', 'u_id', 'j_d_3', '3AB9D23F7A4B3C9B', 'tfstk'
        ]

        # 清理cookies值，移除可能导致编码问题的字符
        cleaned_cookies = {}
        for key, value in cookies.items():
            # 移除Unicode字符，只保留ASCII字符
            cleaned_value = ''.join(char for char in value if ord(char) < 128)
            cleaned_cookies[key] = cleaned_value

        return cleaned_cookies

    def parse_and_update_cookies(self, cookie_text):
        """解析并更新cookies"""
        # 解析cookie文本
        cookies = self.parse_cookie_text(cookie_text)

        # 更新ERP集成模块
        if hasattr(self, 'erp_integration') and self.erp_integration:
            self.erp_integration.update_cookies(cookies)

        # 保存到文件
        self.save_cookies_to_file(cookies)

        return cookies

    def save_cookies_to_file(self, cookies):
        """保存cookies到文件（完全替换）"""
        cookie_file = "latest_cookies.json"
        try:
            # 准备要保存的cookies（完全替换，不合并）
            cookies_to_save = cookies.copy()
            cookies_to_save['_updated_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 完全替换保存到文件
            with open(cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies_to_save, f, indent=2, ensure_ascii=True)

            self.log_message(f"✅ 成功保存 {len(cookies)} 个cookies到 {cookie_file} (完全替换)")

        except Exception as e:
            self.log_message(f"❌ 保存cookies失败: {str(e)}")

    def check_erp_auth_status(self):
        """检查ERP认证状态"""
        if hasattr(self, 'erp_integration') and self.erp_integration:
            self.log_message(f"🔍 ERP认证状态检查")

            # 测试实际连接
            test_result = self.erp_integration.check_auth_status()

            return test_result
        else:
            self.log_message(f"❌ ERP集成模块未初始化")
            return False

    def cleanup(self):
        """清理资源"""
        try:
            # 清理文件管理器的临时文件
            if hasattr(self, 'file_manager'):
                self.file_manager.cleanup_temp_files()
            
            # 清理其他资源
            if hasattr(self, 'image_splitter'):
                self.image_splitter.cleanup()
                
        except Exception as e:
            print(f"清理资源时出错: {e}")

    def get_display_name_for_file(self, file_path):
        """获取文件的显示名称（重命名后的名称或原始名称）"""
        try:
            # 检查重命名缓存
            if (hasattr(self, 'image_rename_cache') and 
                file_path in self.image_rename_cache):
                rename_info = self.image_rename_cache[file_path]
                return rename_info.get('display_name', os.path.basename(file_path))
            
            # 如果没有重命名，返回原始文件名
            return os.path.basename(file_path)
            
        except Exception as e:
            print(f"获取显示名称失败: {e}")
            return os.path.basename(file_path) if file_path else "未知文件"

    # 🔥 新增：双向绑定辅助方法
    def find_result_table_row_by_file_path(self, file_path):
        """根据文件路径查找解析结果表格中对应的行号"""
        try:
            if not hasattr(self, 'result_table') or not self.result_table:
                return -1
            
            display_name = self.get_display_name_for_file(file_path)
            
            # 遍历解析结果表格查找匹配的行
            for row in range(self.result_table.rowCount()):
                file_item = self.result_table.item(row, 1)  # 文件名列
                if file_item and file_item.text() == display_name:
                    return row
            
            return -1
            
        except Exception as e:
            self.log_message(f"查找解析结果行失败: {str(e)}", "ERROR")
            return -1

    def find_thumbnail_by_file_name(self, file_name):
        """根据文件名查找对应的缩略图文件路径"""
        try:
            # 🔥 调试日志：检查输入文件名
            self.log_message(f"🔍 查找文件路径: file_name='{file_name}'")
            
            # 获取所有文件路径
            files = self.file_manager.get_file_list() if hasattr(self, 'file_manager') else []
            self.log_message(f"🔍 当前文件列表数量: {len(files)}")
            
            # 查找匹配的文件路径
            for file_path in files:
                display_name = self.get_display_name_for_file(file_path)
                self.log_message(f"🔍 比较: '{display_name}' vs '{file_name}'")
                if display_name == file_name:
                    self.log_message(f"✅ 找到匹配文件: {file_path}")
                    return file_path
            
            self.log_message(f"⚠️ 未找到匹配的文件路径")
            return None
            
        except Exception as e:
            self.log_message(f"查找缩略图失败: {str(e)}", "ERROR")
            return None

    def sync_thumbnail_selection(self, file_path):
        """同步缩略图选择状态"""
        try:
            if self._syncing_selection:  # 防止无限循环
                return
                
            self._syncing_selection = True
            
            # 🔥 调试日志：检查输入参数
            self.log_message(f"🔍 同步缩略图选择: file_path={file_path}")
            
            # 更新缩略图选择状态
            if hasattr(self, 'image_manager'):
                self.image_manager.update_card_selections(file_path)
                self.image_manager.current_image_path = file_path
                
                # 🔥 调试日志：检查全屏模式状态
                if hasattr(self.image_manager, 'image_stack'):
                    current_index = self.image_manager.image_stack.currentIndex()
                    self.log_message(f"🔍 当前 QStackedWidget 索引: {current_index}")
                    
                    if current_index == 1:
                        # 当前在全屏模式，需要更新全屏显示
                        self.log_message(f"🔍 检测到全屏模式，开始切换大图显示")
                        self.image_manager.load_main_fullscreen_image()
                        self.log_message(f"✅ 已在全屏模式下切换到: {self.get_display_name_for_file(file_path)}")
                    else:
                        self.log_message(f"🔍 当前在缩略图模式 (索引={current_index})")
                else:
                    self.log_message(f"⚠️ image_stack 未找到")
            else:
                self.log_message(f"⚠️ image_manager 未初始化")
            
        except Exception as e:
            self.log_message(f"同步缩略图选择失败: {str(e)}", "ERROR")
        finally:
            self._syncing_selection = False

    def sync_result_table_selection(self, file_path):
        """同步解析结果表格选择状态"""
        try:
            if self._syncing_selection:  # 防止无限循环
                return
                
            self._syncing_selection = True
            
            # 查找对应的解析结果行
            row = self.find_result_table_row_by_file_path(file_path)
            if row >= 0 and hasattr(self, 'result_table') and self.result_table:
                # 选中对应行
                self.result_table.selectRow(row)
                # 滚动到可见区域
                self.result_table.scrollToItem(self.result_table.item(row, 0))
                
        except Exception as e:
            self.log_message(f"同步解析结果选择失败: {str(e)}", "ERROR")
        finally:
            self._syncing_selection = False


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("智能票据处理系统")
    app.setApplicationVersion("2.0")

    # 设置高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 创建并显示主窗口
    window = ModernTicketProcessorGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 